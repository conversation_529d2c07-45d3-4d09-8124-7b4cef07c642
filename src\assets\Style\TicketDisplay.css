
/* === Premium Airline Theme Colors === */
:root {
  --airline-primary: #d71921;    /* Virgin Atlantic red */
  --airline-secondary: #1e293b;  /* Dark blue-gray */
  --airline-accent: #f8fafc;     /* Light gray background */
  --airline-success: #166534;    /* Green */
  --airline-warning: #92400e;    /* Orange */
  --airline-danger: #dc2626;     /* Red */
  --airline-text: #334155;       /* Main text color */
  --airline-border: #e2e8f0;     /* Border color */
}

/* === Premium Airline Theme Colors === */
:root {
  --airline-primary: #d71921;    /* Virgin Atlantic red */
  --airline-secondary: #1e293b;  /* Dark blue-gray */
  --airline-accent: #f8fafc;     /* Light gray background */
  --airline-success: #166534;    /* Green */
  --airline-warning: #92400e;    /* Orange */
  --airline-danger: #dc2626;     /* Red */
  --airline-text: #334155;       /* Main text color */
  --airline-border: #e2e8f0;     /* Border color */
}

/* === Ticket Display Wrapper === */
.ticket-display-wrapper {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* === Ticket Display Container === */
.ticket-display {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  width: 95vw;
  margin: 20px auto;
  max-width: 1800px;
  min-width: 800px;
  overflow-x: auto;
  animation: fadeIn 0.3s ease-in-out;
}

/* === Controls Header === */
.controls-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
  min-width: 1400px;
}

.search-filter {
  display: flex;
  gap: 16px;
  align-items: center;
  flex: 1;
}

.search-input {
  width: 400px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-input input {
  padding: 12px 16px;
  font-size: 14px;
  border: 1px solid #e2e8f0;
}

.actions {
  display: flex;
  gap: 8px;
}

/* === Filter Buttons === */
.filter-buttons {
  background: #ffffff;
  padding: 4px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  display: flex;
  gap: 4px;
}

.filter-buttons .rs-btn {
  padding: 8px 16px;
  font-weight: 500;
  color: #64748b;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

.filter-buttons .active-filter {
  background: #d71921;
  color: #ffffff;
  font-weight: 500;
}

.rs-table-row {
  transition: background-color 0.2s ease;
}

/* === Table General === */
.custom-table {
  font-family: "Helvetica Neue", Arial, sans-serif;
}

.rs-table {
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px !important;
  background: white !important;
}

/* === Table Headers === */
.rs-table-cell-header {
  background-color: #f8fafc !important;
  height: 48px !important;
  padding: 0 !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1 !important;
  border-bottom: 2px solid #e2e8f0 !important;
}

.rs-table-cell-header .rs-table-cell-content {
  height: 100% !important;
  padding: 0 16px !important;
  display: flex !important;
  align-items: center !important;
  font-weight: 600 !important;
  color: #1e293b !important;
  font-size: 13px !important;
}

.rs-table-cell-header-sort-wrapper {
  visibility: visible !important;
  color: #64748b !important;
  margin-left: 4px !important;
}

/* === Table Cells === */
.rs-table-cell .rs-table-cell-content {
  padding: 0 16px !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  color: #334155 !important;
  font-size: 13px !important;
}

.rs-table-row:hover .rs-table-cell {
  background-color: #f8fafc !important;
}

.rs-table-cell {
  border-bottom: 1px solid #e2e8f0 !important;
}

/* === Fixed Columns === */
.rs-table-cell-header[aria-label="PNR"] {
  background-color: #f8fafc !important;
  left: 0 !important;
  z-index: 2 !important;
}

.rs-table-cell[aria-colindex="1"] {
  background-color: white !important;
  position: sticky !important;
  left: 0 !important;
  z-index: 1 !important;
}

/* === Specific Columns Alignment === */
.rs-table-cell[aria-colindex="3"] .rs-table-cell-content,
.rs-table-cell[aria-colindex="7"] .rs-table-cell-content {
  justify-content: flex-end !important;
}

.rs-table-cell-header[aria-label="Fare Amount"] .rs-table-cell-content,
.rs-table-cell-header[aria-label="Refund Amount"] .rs-table-cell-content {
  justify-content: flex-end !important;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Status & Eligibility Badges === */
.status-badge, .eligibility-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.status-badge.completed { background-color: #dcfce7; color: #166534; }
.status-badge.cancelled { background-color: #fee2e2; color: #dc2626; }
.status-badge.pending { background-color: #fef3c7; color: #92400e; }

.eligibility-badge.yes { background-color: #dcfce7; color: #166534; }
.eligibility-badge.no { background-color: #fee2e2; color: #dc2626; }

/* === Pagination === */
.pagination-container {
  margin-top: 16px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rs-pagination-btn {
  min-width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rs-pagination-btn.rs-pagination-btn-active {
  background-color: #dc2626;
  color: white;
  border: none;
}

/* === Itinerary Section === */
.itinerary-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.itinerary-info .rs-table-cell-content {
  padding: 8px 12px !important;
  font-size: 13px !important;
  color: #334155 !important;
  display: flex;
  align-items: center;
}

.itinerary-info .rs-table-cell-header .rs-table-cell-content {
  background-color: #f1f5f9 !important;
  font-weight: 600 !important;
  color: #1e293b !important;
}

/* === Passenger Information (Modal) === */
.ticket-details {
  padding: 20px;
}

.passenger-info h4,
.itinerary-info h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.passenger-info p {
  margin: 8px 0;
  color: #666;
}

/* === Animations === */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* === Responsive === */
@media (max-width: 1024px) {
  .rs-table {
    min-width: 100%;
    overflow-x: auto;
  }

  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .search-input {
    width: 100%;
  }

  .controls-container {
    min-width: auto;
    flex-direction: column;
    align-items: stretch;
  }

  .search-filter {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-buttons {
    justify-content: center;
  }
}

/* === Mobile Responsive === */
@media (max-width: 768px) {
  .ticket-display {
    width: 100%;
    margin: 10px auto;
    padding: 16px;
  }

  .rs-table-cell-content {
    padding: 6px 8px !important;
    font-size: 12px !important;
  }

  .rs-table-cell-header .rs-table-cell-content {
    padding: 6px 8px !important;
    font-size: 11px !important;
  }

  .search-input {
    width: 100%;
  }

  .actions {
    flex-direction: column;
    width: 100%;
  }

  .actions .rs-btn {
    width: 100%;
  }
}

/* === Extra Small Screens === */
@media (max-width: 480px) {
  .rs-table-cell-content {
    padding: 4px 6px !important;
    font-size: 11px !important;
  }

  .rs-table-cell-header .rs-table-cell-content {
    padding: 4px 6px !important;
    font-size: 10px !important;
  }

  .status-badge, .eligibility-badge {
    padding: 2px 6px;
    font-size: 10px;
  }
}

/* PNR Details Styling */
.pnr-details-container {
  padding: 16px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.pnr-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

/* Customer Details Styling */
.customer-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.customer-card {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e0e0e0;
}

.customer-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.customer-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;
}

/* Segment Details Styling */
.segment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.segment-detail-card {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e0e0e0;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.segment-route {
  display: flex;
  align-items: center;
  gap: 8px;
}

.origin, .destination {
  font-weight: bold;
  font-size: 16px;
}

.route-arrow {
  color: #999;
}

.flight-info {
  font-size: 14px;
  color: #666;
}

.segment-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-size: 12px;
  color: #666;
}

.detail-value {
  font-size: 14px;
  color: #333;
}

/* Price Details Styling */
.price-card {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  text-align: center;
}

.price-amount {
  font-size: 24px;
  font-weight: 600;
  color: #d71921; /* Virgin Atlantic red */
  margin-bottom: 8px;
}

.currency-info {
  font-size: 14px;
  color: #666;
}

.no-data {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

/* Verification Section Styling */
.verification-section {
  margin: 20px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.verification-status {
  margin-top: 12px;
}

/* Idle state */
.verification-idle {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px;
  text-align: center;
}

.verification-note {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* Verifying state */
.verification-loading {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.verification-animation {
  display: flex;
  justify-content: center;
  align-items: center;
}

.verification-message {
  flex: 1;
}

.agent-message {
  margin: 0 0 8px 0;
  font-size: 15px;
  color: #333;
}

.agent-badge {
  background-color: #d71921; /* Virgin Atlantic red */
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-right: 8px;
}

.wait-time {
  margin: 0;
  font-size: 13px;
  color: #666;
}

/* Verified state */
.verification-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px;
  text-align: center;
  background-color: #dcfce7;
  border-radius: 8px;
  border: 1px solid #86efac;
}

.success-icon {
  width: 40px;
  height: 40px;
  background-color: #16a34a;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
}

.success-message {
  margin: 0;
  color: #166534;
  font-weight: 500;
}

.loading-details {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #166534;
  font-size: 14px;
}

/* Failed state */
.verification-failed {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px;
  text-align: center;
  background-color: #fee2e2;
  border-radius: 8px;
  border: 1px solid #fca5a5;
}

.failed-icon {
  width: 40px;
  height: 40px;
  background-color: #dc2626;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
}

.failed-message {
  margin: 0;
  color: #b91c1c;
  font-weight: 500;
}

/* Itinerary Summary Styling */
.itinerary-summary {
  margin-top: 20px;
}

.progress-container {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  margin: 8px 0 16px 0;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #d71921; /* Virgin Atlantic red */
  border-radius: 4px;
  transition: width 0.3s ease;
}

.itinerary-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.itinerary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.itinerary-segment {
  display: flex;
  align-items: center;
  gap: 8px;
}

.segment-number {
  width: 24px;
  height: 24px;
  background-color: #f1f5f9;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
}

.segment-route {
  font-size: 14px;
  color: #333;
}

.segment-status {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
}

.segment-status.completed {
  background-color: #dcfce7;
  color: #166534;
}

.segment-status.cancelled {
  background-color: #fee2e2;
  color: #dc2626;
}

.segment-status.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.no-itinerary {
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px dashed #e0e0e0;
}

/* Verification Streaming UI */
.verification-streaming {
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 16px;
  margin-top: 12px;
}

.agent-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.agent-avatar {
  width: 40px;
  height: 40px;
  background-color: #d71921; /* Virgin Atlantic red */
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.agent-avatar.secondary {
  background-color: #3b82f6; /* Blue for secondary agent */
}

.agent-info {
  display: flex;
  flex-direction: column;
}

.agent-name {
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.agent-status {
  font-size: 12px;
  color: #666;
}

.verification-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.verification-step {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.verification-step.current {
  opacity: 1;
}

.verification-step.complete {
  opacity: 0.8;
}

.step-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #666;
  flex-shrink: 0;
  margin-top: 2px;
}

.verification-step.current .step-indicator {
  background-color: #d71921; /* Virgin Atlantic red */
  color: white;
}

.verification-step.complete .step-indicator {
  background-color: #16a34a;
  color: white;
}

.step-complete {
  font-size: 14px;
  font-weight: bold;
}

.step-message {
  font-size: 14px;
  color: #333;
  padding-top: 2px;
  display: flex;
  align-items: center;
}

.typing-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
}

.typing-indicator .dot {
  width: 4px;
  height: 4px;
  background-color: #666;
  border-radius: 50%;
  margin: 0 2px;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator .dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

.transition-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #e0f2fe;
  border-radius: 6px;
  color: #0369a1;
  font-size: 14px;
  margin-top: 8px;
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.loading-details {
  width: 100%;
  margin-top: 16px;
  animation: fadeIn 0.5s ease-in;
}

.loading-animation {
  margin-top: 12px;
  padding: 16px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border: 1px dashed #bae6fd;
}

.data-stream {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 16px;
}

.data-line {
  height: 6px;
  background: linear-gradient(90deg, #bae6fd 30%, #e0f2fe 50%, #bae6fd 70%);
  border-radius: 3px;
  animation: dataStream 2s infinite linear;
  opacity: 0.7;
}

.data-line:nth-child(1) {
  width: 100%;
}

.data-line:nth-child(2) {
  width: 85%;
  animation-delay: 0.5s;
}

.data-line:nth-child(3) {
  width: 70%;
  animation-delay: 0.3s;
}

.data-line:nth-child(4) {
  width: 90%;
  animation-delay: 0.7s;
}

@keyframes dataStream {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* Processing steps animation */
.processing-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.processing-step {
  display: flex;
  align-items: center;
  gap: 10px;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.processing-step.active {
  opacity: 1;
}

.step-dot {
  width: 12px;
  height: 12px;
  background-color: #3b82f6;
  border-radius: 50%;
  display: block;
  position: relative;
}

.processing-step.active .step-dot::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.step-text {
  font-size: 14px;
  color: #1e40af;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.4;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

/* Add JavaScript to cycle through active steps */
@keyframes cycleSteps {
  0%, 20% {
    opacity: 1;
  }
  25%, 100% {
    opacity: 0.5;
  }
}

.processing-step:nth-child(1) {
  animation: cycleSteps 8s infinite;
  animation-delay: 0s;
}

.processing-step:nth-child(2) {
  animation: cycleSteps 8s infinite;
  animation-delay: 2s;
}

.processing-step:nth-child(3) {
  animation: cycleSteps 8s infinite;
  animation-delay: 4s;
}

.processing-step:nth-child(4) {
  animation: cycleSteps 8s infinite;
  animation-delay: 6s;
}

/* Visual Workflow Styling */
.ticket-workflow-visual {
  margin: 24px 0;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.ticket-workflow-visual h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  color: #0f172a;
  font-weight: 600;
}

.workflow-visual-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  overflow-x: auto;
}

/* Node styling */
.workflow-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 120px;
  max-width: 150px;
  position: relative;
  padding: 10px;
  border-radius: 8px;
  background-color: white;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.workflow-node:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.node-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  font-size: 20px;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.node-label {
  font-weight: 600;
  font-size: 14px;
  color: #334155;
  margin-bottom: 6px;
}

.node-description {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
}

.node-amount {
  margin-top: 8px;
  font-weight: 600;
  color: #10b981;
  background-color: #ecfdf5;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

/* Connector styling */
.workflow-connector {
  flex: 1;
  height: 2px;
  position: relative;
  min-width: 40px;
  max-width: 80px;
  transition: all 0.3s ease;
}

.connector-line {
  height: 2px;
  background-color: #cbd5e1;
  width: 100%;
}

.connector-arrow {
  position: absolute;
  right: -5px;
  top: -9px;
  font-size: 20px;
  color: #94a3b8;
}

/* Status styling */
.workflow-node.disabled {
  opacity: 0.6;
}

.workflow-node.disabled .node-icon {
  background-color: #94a3b8 !important;
}

.workflow-node.pending .node-icon {
  animation: pulse 2s infinite;
}

.workflow-connector.disabled .connector-line {
  background-color: #e2e8f0;
  border-top: 2px dashed #cbd5e1;
  height: 0px;
}

.workflow-connector.disabled .connector-arrow {
  color: #cbd5e1;
}

/* Animation */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(14, 165, 233, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(14, 165, 233, 0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .workflow-visual-container {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  
  .workflow-node {
    min-width: 200px;
    max-width: 100%;
    width: 100%;
  }
  
  .workflow-connector {
    width: 2px;
    height: 40px;
    min-width: auto;
    max-width: auto;
  }
  
  .connector-line {
    width: 2px;
    height: 100%;
  }
  
  .connector-arrow {
    transform: rotate(90deg);
    right: -9px;
    top: auto;
    bottom: -5px;
  }
}

/* Verification Workflow Styling */
.verification-workflow {
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  padding: 20px;
  margin-bottom: 16px;
}

.verification-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  flex-wrap: wrap; /* Allow buttons to wrap on smaller screens */
}

/* Node spinner animation */
.node-spinner {
  position: absolute;
  top: -4px;
  right: -4px;
  bottom: -4px;
  left: -4px;
  border: 2px solid transparent;
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Active node styling */
.workflow-node.active .node-icon {
  animation: pulse 2s infinite;
}

.workflow-node.failed .node-icon {
  background-color: #ef4444 !important;
}

.workflow-node.completed .node-icon::after {
  content: '✓';
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #16a34a;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border: 2px solid white;
}

/* Verification steps container */
.verification-steps-container {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px dashed #cbd5e1;
  overflow: hidden;
  height: auto; /* Allow height to adjust based on content */
  min-height: 100px; /* Minimum height to prevent small fluctuations */
}

/* Loading animation for data retrieval */
.loading-animation {
  margin-top: 16px;
  height: 100px; /* Fixed height */
  overflow: hidden;
}

.data-stream {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background-color: #f1f5f9;
  border-radius: 8px;
}

.data-line {
  height: 8px;
  background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 50%, #e2e8f0 100%);
  border-radius: 4px;
  animation: dataLoad 2s infinite;
  opacity: 0.7;
}

.data-line:nth-child(2) {
  width: 85%;
  animation-delay: 0.2s;
}

.data-line:nth-child(3) {
  width: 70%;
  animation-delay: 0.4s;
}

.data-line:nth-child(4) {
  width: 90%;
  animation-delay: 0.6s;
}

@keyframes dataLoad {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* Add these new styles for the current step indicator and next arrow */
.current-step-indicator {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #f97316; /* Orange */
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 1.5s infinite;
  z-index: 10; /* Ensure it's above other elements */
}

.workflow-node.current-stage {
  border: 2px solid #f97316; /* Orange border */
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.2);
  transform: translateY(-5px);
}

.workflow-connector.next-step .connector-line {
  background-color: #16a34a; /* Green */
  height: 3px;
}

.connector-arrow.next-arrow {
  color: #16a34a; /* Green */
  font-size: 24px;
  font-weight: bold;
  animation: arrowPulse 1.5s infinite;
}

@keyframes arrowPulse {
  0%, 100% {
    transform: translateX(0);
    opacity: 1;
  }
  50% {
    transform: translateX(5px);
    opacity: 0.7;
  }
}

/* Update existing styles */
.workflow-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 120px;
  max-width: 150px;
  position: relative;
  padding: 10px;
  border-radius: 8px;
  background-color: white;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.workflow-connector {
  flex: 1;
  height: 2px;
  position: relative;
  min-width: 40px;
  max-width: 80px;
  transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .workflow-visual-container {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  
  .workflow-node {
    min-width: 200px;
    max-width: 100%;
    width: 100%;
  }
  
  .workflow-connector {
    width: 2px;
    height: 40px;
    min-width: auto;
    max-width: auto;
  }
  
  .connector-line {
    width: 2px;
    height: 100%;
  }
  
  .connector-arrow {
    transform: rotate(90deg);
    right: -9px;
    top: auto;
    bottom: -5px;
  }
}

/* Verification Workflow Styling */
.verification-workflow {
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  padding: 20px;
  margin-bottom: 16px;
}

.verification-actions {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

/* Node spinner animation */
.node-spinner {
  position: absolute;
  top: -4px;
  right: -4px;
  bottom: -4px;
  left: -4px;
  border: 2px solid transparent;
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Active node styling */
.workflow-node.active .node-icon {
  animation: pulse 2s infinite;
}

.workflow-node.failed .node-icon {
  background-color: #ef4444 !important;
}

.workflow-node.completed .node-icon::after {
  content: '✓';
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #16a34a;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border: 2px solid white;
}

/* Verification steps container */
.verification-steps-container {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px dashed #cbd5e1;
}

/* Loading animation for data retrieval */
.loading-animation {
  margin-top: 16px;
}

.data-stream {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background-color: #f1f5f9;
  border-radius: 8px;
}

.data-line {
  height: 8px;
  background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 50%, #e2e8f0 100%);
  border-radius: 4px;
  animation: dataLoad 2s infinite;
  opacity: 0.7;
}

.data-line:nth-child(2) {
  width: 85%;
  animation-delay: 0.2s;
}

.data-line:nth-child(3) {
  width: 70%;
  animation-delay: 0.4s;
}

.data-line:nth-child(4) {
  width: 90%;
  animation-delay: 0.6s;
}

@keyframes dataLoad {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* Style for the refund eligibility button */
.refund-eligibility-btn {
  background-color: #10b981 !important; /* Emerald green */
  color: white !important;
  border: none !important;
  transition: background-color 0.2s ease !important;
}

.refund-eligibility-btn:hover {
  background-color: #059669 !important; /* Darker green on hover */
}

/* Make buttons responsive */
@media (max-width: 576px) {
  .verification-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .verification-actions .rs-btn {
    width: 100%;
  }
}

/* Add a completed state style for the workflow */
.workflow-node.completed {
  border-color: #10b981; /* Green border for completed nodes */
}

.workflow-node.completed .node-icon {
  background-color: #10b981 !important; /* Override inline style */
}

/* Add a check mark to completed nodes */
.workflow-node.completed .node-icon::after {
  content: '✓';
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #16a34a;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border: 2px solid white;
}

/* Make connectors green when connecting completed nodes */
.workflow-node.completed + .workflow-connector .connector-line {
  background-color: #10b981; /* Green */
}

.workflow-node.completed + .workflow-connector .connector-arrow {
  color: #10b981; /* Green */
}

/* Fix the workflow container to prevent fluctuation */
.workflow-container {
  width: 100%;
  height: 180px; /* Fixed height to prevent fluctuation */
  overflow: hidden;
  margin: 0 auto;
  position: relative;
}

.workflow-visual-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  overflow: hidden;
  position: absolute; /* Position absolutely to prevent layout shifts */
  top: 50%; /* Center vertically */
  left: 0;
  right: 0;
  transform: translateY(-50%); /* Center vertically */
  width: 100%;
}

/* Fix node heights to prevent fluctuation */
.workflow-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100px;
  height: 120px; /* Fixed height */
  position: relative;
  padding: 8px;
  border-radius: 8px;
  background-color: white;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  flex-shrink: 0;
  margin: 0 5px;
}

/* Fix the position of the current step indicator */
.current-step-indicator {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #f97316; /* Orange */
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 1.5s infinite;
  z-index: 10; /* Ensure it's above other elements */
}

/* Fix the height of the node description */
.node-description {
  font-size: 11px;
  color: #64748b;
  width: 100%;
  height: 40px; /* Fixed height */
  overflow: hidden;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* Fix the verification steps container */
.verification-steps-container {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px dashed #cbd5e1;
  overflow: hidden;
  height: auto; /* Allow height to adjust based on content */
  min-height: 100px; /* Minimum height to prevent small fluctuations */
}

/* Fix the loading animation container */
.loading-animation {
  margin-top: 16px;
  height: 100px; /* Fixed height */
  overflow: hidden;
}

/* Add a completed state style for the workflow */
.workflow-node.completed {
  border-color: #10b981; /* Green border for completed nodes */
}

.workflow-node.completed .node-icon {
  background-color: #10b981 !important; /* Override inline style */
}

/* Add a check mark to completed nodes */
.workflow-node.completed .node-icon::after {
  content: '✓';
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #16a34a;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Structured view styling */
.structured-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #212529;
  display: flex;
  align-items: center;
  gap: 6px;
}

.section-icon {
  font-size: 18px;
}

/* Passenger card styling */
.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
}

.passenger-card {
  background-color: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e9ecef;
}

.passenger-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
  color: #212529;
}

.passenger-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Flight segment styling */
.segment-card {
  background-color: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e9ecef;
}

/* Refund Details Modal Styling */
.refund-details-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.refund-status-header {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.refund-eligible {
  display: flex;
  align-items: center;
  gap: 15px;
  background-color: rgba(25, 135, 84, 0.1);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(25, 135, 84, 0.2);
}

.refund-not-eligible {
  display: flex;
  align-items: center;
  gap: 15px;
  background-color: rgba(220, 53, 69, 0.1);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.status-icon {
  font-size: 24px;
}

.status-text h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
}

.status-text p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.refund-amount-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 20px;
}

.refund-amount-section h5 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.refund-amount-section p {
  font-size: 14px;
  color: #333;
}

.refund-amount-section .amount {
  font-size: 18px;
  font-weight: 600;
  color: #d71921;
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
.refund-amount-section {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  text-align: center;
}

.amount-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 5px;
}

.amount-value {
  font-size: 24px;
  font-weight: 700;
  color: #198754;
}

.deductions-section {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.deductions-section h5 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #212529;
}

.deductions-table {
  width: 100%;
  border-collapse: collapse;
}

.deductions-table th,
.deductions-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.deductions-table th {
  font-weight: 600;
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
}

.deductions-table td {
  font-size: 14px;
}

.processing-info {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: rgba(13, 110, 253, 0.1);
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(13, 110, 253, 0.2);
}

.info-icon {
  font-size: 18px;
}

.info-text {
  font-size: 14px;
  color: #0d6efd;
}
  margin-bottom: 12px;
}

.segment-card:last-child {
  margin-bottom: 0;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.flight-number {
  font-weight: 600;
  font-size: 14px;
  color: #e31837;
}

.flight-date {
  font-size: 13px;
  color: #6c757d;
}

.segment-route {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.departure, .arrival {
  flex: 1;
}

/* Enhanced ticket info styling */
.ticket-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.03);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 10px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.05);
}

.info-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
}

/* Enhanced status badges */
.status-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.status-completed {
  background-color: rgba(25, 135, 84, 0.1);
  color: #198754;
  border: 1px solid rgba(25, 135, 84, 0.2);
}

.status-pending {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-cancelled {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .ticket-info {
    grid-template-columns: 1fr;
    gap: 10px;
    padding: 12px;
  }
  
  .info-item {
    padding: 8px;
  }
  
  .info-value {
    font-size: 14px;
  }


.airport-code {
  font-size: 18px;
  font-weight: 700;
  color: #212529;
}

.airport-name {
  font-size: 12px;
  color: #6c757d;
}

.time {
  font-size: 14px;
  font-weight: 600;
  color: #212529;
}

.route-line {
  flex: 2;
  height: 2px;
  background-color: #e9ecef;
  position: relative;
  margin: 0 10px;
}

.duration {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  padding: 0 5px;
  font-size: 11px;
  color: #6c757d;
}

.segment-details {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
}

/* Fare information styling */
.fare-details {
  background-color: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e9ecef;
}

.fare-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  border-bottom: 1px solid #e9ecef;
}

.fare-item:last-child {
  border-bottom: none;
}

.fare-item.total {
  font-weight: 600;
  font-size: 15px;
  margin-top: 5px;
  padding-top: 10px;
  border-top: 2px solid #e9ecef;
}

.fare-label {
  color: #6c757d;
}

.fare-value {
  color: #212529;
}

/* Additional information styling */
.additional-info {
  background-color: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e9ecef;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.info-label {
  font-size: 12px;
  color: #6c757d;
  text-transform: capitalize;
}

.info-value {
  font-size: 13px;
  color: #212529;
}

/* Detail item styling */
.detail-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
}

.detail-label {
  color: #6c757d;
}

.detail-value {
  color: #212529;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .segment-route {
    flex-direction: column;
    gap: 15px;
  }
  
  .route-line {
    width: 2px;
    height: 30px;
    margin: 5px 0;
  }
  
  .duration {
    top: 50%;
    left: -25px;
    transform: translateY(-50%);
  }
  
  .segment-details {
    grid-template-columns: 1fr;
  }


.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--airline-primary);
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: var(--airline-border);
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--airline-primary);
}

/* === Enhanced Status Badges === */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === Premium Flight Card === */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid var(--airline-primary);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* === Flight Timeline === */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}
