import React, { useState, useEffect, useRef } from 'react';
import { useOptimistic } from 'react';
import {
  Table, InputGroup, Input, Button, ButtonGroup, Pagination, Modal, Message, Loader
} from 'rsuite';
import { Search, Filter, Download, RefreshCw } from 'react-feather';
import '../assets/Style/TicketDisplay.css';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css'; // Import the CSS
import { callOrchestrator } from "../Services/api";
import { useFormStatus } from 'react-dom';
import AirlineTicket from './premium/AirlineTicket';

const API_URL = "http://localhost:5120";

const { Column, HeaderCell, Cell } = Table;

const TicketWorkflowVisual = ({ status, refundEligible, refundAmount }) => {
  // Define workflow steps with icons and colors
  const steps = [
    { 
      id: 1, 
      name: "Booking Created", 
      icon: "📝", 
      color: "#e11d48", // Red
      status: "completed",
      description: "Ticket reservation created in the system"
    },
    { 
      id: 2, 
      name: "Payment Processed", 
      icon: "💳", 
      color: "#4f46e5", // Indigo
      status: "completed",
      description: "Payment confirmed and processed"
    },
    { 
      id: 3, 
      name: "Ticket Issued", 
      icon: "🎫", 
      color: "#f59e0b", // Amber
      status: "completed",
      description: "E-ticket issued to passenger"
    },
    { 
      id: 4, 
      name: status === "CANCELLED" ? "Booking Cancelled" : "Journey Status", 
      icon: status === "CANCELLED" ? "❌" : status === "COMPLETED" ? "✅" : "⏳", 
      color: status === "CANCELLED" ? "#dc2626" : status === "COMPLETED" ? "#16a34a" : "#0ea5e9", // Red, Green, or Blue
      status: status === "PENDING" ? "pending" : "completed",
      description: status === "CANCELLED" ? "Booking has been cancelled" : 
                  status === "COMPLETED" ? "Journey has been completed" : "Journey is pending"
    },
    { 
      id: 5, 
      name: "Refund Status", 
      icon: "💰", 
      color: "#10b981", // Emerald
      status: refundEligible && refundAmount > 0 ? "completed" : "disabled",
      description: refundAmount > 0 ? `Refund of ₹${refundAmount.toLocaleString()} processed` : "No refund applicable",
      amount: refundAmount > 0 ? `₹${refundAmount.toLocaleString()}` : null
    }
  ];

  return (
    <div className="ticket-workflow-visual">
      <h4>Ticket Processing Workflow</h4>
      <div className="workflow-visual-container">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            <div className={`workflow-node ${step.status}`}>
              <div className="node-icon" style={{ backgroundColor: step.color }}>
                <span>{step.icon}</span>
              </div>
              <div className="node-label">{step.name}</div>
              <div className="node-description">{step.description}</div>
              {step.amount && (
                <div className="node-amount">{step.amount}</div>
              )}
            </div>
            {index < steps.length - 1 && (
              <div className={`workflow-connector ${steps[index + 1].status === 'disabled' ? 'disabled' : 'active'}`}>
                <div className="connector-line"></div>
                <div className="connector-arrow">→</div>
              </div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

// Add this new component for the verification workflow
const VerificationWorkflow = ({ status, currentStep, steps, orchestratorLoading }) => {
  // Define the workflow stages
  const workflowStages = [
    {
      id: 1,
      name: "PNR Verification",
      icon: "🔍",
      color: "#4f46e5", // Indigo
      status: status === 'idle' ? 'pending' : 
              status === 'verifying' ? 'active' : 
              status === 'verified' ? 'completed' : 'failed',
      description: status === 'idle' ? "Verify PNR to see detailed information" :
                  status === 'verifying' ? "Verifying PNR information..." :
                  status === 'verified' ? "PNR successfully verified" : "Verification failed"
    },
    {
      id: 2,
      name: "Retrieve Details",
      icon: "📋",
      color: "#0ea5e9", // Sky blue
      status: status !== 'verified' ? 'disabled' :
              orchestratorLoading ? 'active' : 'completed',
      description: status !== 'verified' ? "Waiting for verification" :
                  orchestratorLoading ? "Retrieving detailed information..." : 
                  "Flight details retrieved successfully"
    },
    {
      id: 3,
      name: "Display Itinerary",
      icon: "✈️",
      color: "#10b981", // Emerald
      status: status !== 'verified' || orchestratorLoading ? 'disabled' : 'completed',
      description: status !== 'verified' ? "Waiting for verification" :
                  orchestratorLoading ? "Waiting for details..." : 
                  "Detailed itinerary ready to view"
    }
  ];

  // Determine the current active stage index
  let activeStageIndex = -1;
  if (status === 'verifying') {
    activeStageIndex = 0;
  } else if (status === 'verified' && orchestratorLoading) {
    activeStageIndex = 1;
  } else if (status === 'verified' && !orchestratorLoading) {
    // Don't set an active stage when all steps are completed
    activeStageIndex = -1; // This removes the "Processing" indicator at the end
  }

  return (
    <div className="verification-workflow">
      {/* Fixed height container to prevent fluctuation */}
      <div className="workflow-container">
        <div className="workflow-visual-container">
          {workflowStages.map((stage, index) => (
            <React.Fragment key={stage.id}>
              <div className={`workflow-node ${stage.status} ${index === activeStageIndex ? 'current-stage' : ''}`}>
                <div className="node-icon" style={{ backgroundColor: stage.color }}>
                  <span>{stage.icon}</span>
                  {stage.status === 'active' && <div className="node-spinner"></div>}
                </div>
                <div className="node-label">{stage.name}</div>
                <div className="node-description">{stage.description}</div>
                {index === activeStageIndex && (
                  <div className="current-step-indicator">Processing</div>
                )}
              </div>
              {index < workflowStages.length - 1 && (
                <div className={`workflow-connector ${workflowStages[index + 1].status === 'disabled' ? 'disabled' : 
                                index === activeStageIndex ? 'next-step' : 'active'}`}>
                  <div className="connector-line"></div>
                  {index === activeStageIndex && stage.status === 'completed' && (
                    <div className="connector-arrow next-arrow">→</div>
                  )}
                  {!(index === activeStageIndex && stage.status === 'completed') && (
                    <div className="connector-arrow">→</div>
                  )}
                </div>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
      
      {/* Show current verification step details if verifying */}
      {status === 'verifying' && steps && steps.length > 0 && (
        <div className="verification-steps-container">
          <div className="agent-header">
            <div className="agent-avatar">
              <span>AI</span>
            </div>
            <div className="agent-info">
              <div className="agent-name">PNR Verification Agent</div>
              <div className="agent-status">Working on your request</div>
            </div>
          </div>
          
          <div className="verification-steps">
            {steps.map((step, index) => (
              <div 
                key={step.id} 
                className={`verification-step ${index === currentStep ? 'current' : ''} ${step.complete ? 'complete' : ''}`}
              >
                <div className="step-indicator">
                  {step.complete ? (
                    <span className="step-complete">✓</span>
                  ) : index === currentStep ? (
                    <Loader size="xs" />
                  ) : (
                    <span className="step-number">{step.id}</span>
                  )}
                </div>
                <div className="step-message">
                  {step.message}
                  {index === currentStep && !step.complete && (
                    <span className="typing-indicator">
                      <span className="dot"></span>
                      <span className="dot"></span>
                      <span className="dot"></span>
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Show orchestrator loading animation if retrieving details */}
      {status === 'verified' && orchestratorLoading && (
        <div className="verification-steps-container">
          <div className="agent-header">
            <div className="agent-avatar secondary">
              <span>PD</span>
            </div>
            <div className="agent-info">
              <div className="agent-name">PNR Details Agent</div>
              <div className="agent-status">Retrieving your itinerary</div>
            </div>
          </div>
          <div className="loading-animation">
            <div className="data-stream">
              <div className="data-line"></div>
              <div className="data-line"></div>
              <div className="data-line"></div>
              <div className="data-line"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Create a submit button component with pending state
function VerifyButton() {
  const { pending } = useFormStatus();
  return (
    <Button 
      appearance="primary" 
      type="submit" 
      disabled={pending}
    >
      {pending ? "Verifying..." : "Verify PNR"}
    </Button>
  );
}

const Interactive3DTicket = ({ ticket }) => {
  const ticketRef = useRef(null);
  const [rotation, setRotation] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  
  const handleMouseMove = (e) => {
    if (!ticketRef.current) return;
    const rect = ticketRef.current.getBoundingClientRect();
    const x = (e.clientY - rect.top) / rect.height - 0.5;
    const y = (e.clientX - rect.left) / rect.width - 0.5;
    setRotation({ x: x * 15, y: y * 15 });
  };
  
  return (
    <div 
      className="ticket-3d-container"
      ref={ticketRef}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        perspective: '1000px',
        height: '180px',
        width: '320px',
        margin: '0 auto 20px',
        cursor: 'pointer'
      }}
    >
      <div 
        className="ticket-3d"
        style={{
          transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) ${isHovered ? 'translateZ(10px)' : ''}`,
          transition: 'transform 0.1s ease, box-shadow 0.3s ease',
          transformStyle: 'preserve-3d',
          height: '100%',
          width: '100%',
          background: '#fff',
          borderRadius: '8px',
          padding: '12px',
          color: '#333',
          boxShadow: isHovered 
            ? '0 10px 20px rgba(0,0,0,0.2), 0 5px 10px rgba(0,0,0,0.1)' 
            : '0 5px 15px rgba(0,0,0,0.1)',
          border: '1px solid #e0e0e0',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Virgin Atlantic Red Banner */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '30px',
          background: '#e31837', // Virgin Atlantic red
          zIndex: 1
        }}></div>
        
        {/* Virgin Atlantic Logo */}
        <div style={{
          position: 'absolute',
          top: '6px',
          left: '12px',
          color: 'white',
          fontWeight: 'bold',
          fontSize: '14px',
          zIndex: 2,
          display: 'flex',
          alignItems: 'center'
        }}>
          <span style={{ marginRight: '4px' }}>✈</span>
          <span>VIRGIN ATLANTIC</span>
        </div>
        
        {/* E-Ticket Label */}
        <div style={{
          position: 'absolute',
          top: '8px',
          right: '12px',
          color: 'white',
          fontSize: '10px',
          zIndex: 2,
          textTransform: 'uppercase',
          letterSpacing: '1px'
        }}>
          E-Ticket
        </div>
        
        {/* Main Content */}
        <div style={{ 
          marginTop: '40px', 
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(100% - 40px)'
        }}>
          {/* PNR and Passenger */}
          <div style={{ marginBottom: '10px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between',
              marginBottom: '8px'
            }}>
              <div>
                <div style={{ fontSize: '10px', color: '#666' }}>Booking Reference</div>
                <div style={{ 
                  fontSize: '16px', 
                  fontWeight: '700',
                  color: '#e31837' // Virgin Atlantic red
                }}>
                  {ticket.pnr}
                </div>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontSize: '10px', color: '#666' }}>Status</div>
                <div style={{ 
                  fontSize: '12px', 
                  fontWeight: '600',
                  color: ticket.status === 'COMPLETED' ? '#10b981' : '#e31837'
                }}>
                  {ticket.status}
                </div>
              </div>
            </div>
            
            <div>
              <div style={{ fontSize: '10px', color: '#666' }}>Passenger</div>
              <div style={{ 
                fontSize: '14px', 
                fontWeight: '600',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: 'nowrap'
              }}>
                {ticket.passengerName}
              </div>
            </div>
          </div>
          
          {/* Divider */}
          <div style={{ 
            height: '1px', 
            background: '#e0e0e0', 
            margin: '5px 0',
            backgroundImage: 'linear-gradient(to right, #e0e0e0 50%, transparent 50%)',
            backgroundSize: '8px 1px'
          }}></div>
          
          {/* Flight Info Placeholder */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between',
            fontSize: '12px',
            marginTop: '5px'
          }}>
            <div>
              <div style={{ color: '#666' }}>From - To</div>
              <div style={{ fontWeight: '600' }}>LHR - JFK</div>
            </div>
            <div style={{ textAlign: 'right' }}>
              <div style={{ color: '#666' }}>Flight</div>
              <div style={{ fontWeight: '600' }}>VS001</div>
            </div>
          </div>
          
          {/* Price */}
          <div style={{ 
            marginTop: 'auto',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-end'
          }}>
            <div style={{ 
              fontSize: '10px', 
              color: '#666',
              textTransform: 'uppercase',
              letterSpacing: '1px'
            }}>
              Total Fare
            </div>
            <div style={{ 
              fontSize: '16px', 
              fontWeight: '700',
              color: '#e31837' // Virgin Atlantic red
            }}>
              ₹{ticket.fareAmount.toLocaleString()}
            </div>
          </div>
        </div>
        
        {/* Barcode */}
        <div style={{ 
          position: 'absolute', 
          bottom: '8px', 
          left: '12px',
          right: '12px',
          height: '16px',
          background: `repeating-linear-gradient(
            90deg,
            #000,
            #000 1px,
            transparent 1px,
            transparent 3px
          )`,
          opacity: 0.7
        }}></div>
      </div>
    </div>
  );
};

const FormattedResponse = ({ text }) => {
  if (!text) return <Message type="info" description="No response data available" />;
  
  // Split the text by emoji section headers
  const sections = [];
  const lines = text.split('\n');
  let currentSection = { title: 'General Information', content: [] };
  
  // Regex to match emoji section headers
  const sectionHeaderRegex = /^([\p{Emoji}\p{Emoji_Presentation}🛫🔍🎫🧳💸👤📅✈️🏨🚗📱🔄ℹ️👥💳🍽️🛄🔖🔗])\s*\*\*([^*:]+):\*\*/u;
  
  lines.forEach(line => {
    const match = line.match(sectionHeaderRegex);
    if (match) {
      // Save previous section if it has content
      if (currentSection.content.length > 0) {
        sections.push(currentSection);
      }
      // Start a new section
      currentSection = {
        emoji: match[1],
        title: match[2].trim(),
        content: []
      };
    } else if (line.trim()) {
      // Add non-empty lines to current section
      currentSection.content.push(line);
    }
  });
  
  // Add the last section
  if (currentSection.content.length > 0) {
    sections.push(currentSection);
  }
  
  return (
    <div className="formatted-response" style={{ fontFamily: 'Helvetica Neue, Arial, sans-serif' }}>
      {sections.map((section, index) => (
        <div key={index} style={{ 
          marginBottom: '20px',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          overflow: 'hidden'
        }}>
          <div style={{ 
            display: 'flex',
            alignItems: 'center',
            padding: '12px 15px',
            backgroundColor: '#f8f9fa',
            borderBottom: '1px solid #e0e0e0'
          }}>
            {section.emoji && <span style={{ fontSize: '20px', marginRight: '10px' }}>{section.emoji}</span>}
            <span style={{ fontSize: '16px', fontWeight: 600, color: '#d71921' }}>{section.title}</span>
          </div>
          <div style={{ padding: '15px', lineHeight: 1.6 }}>
            {section.content.map((line, lineIndex) => {
              const formattedLine = line.replace(/\*\*(.*?)\*\*/g, '<span style="font-weight: 600">$1</span>');
              
              return (
                <div 
                  key={lineIndex} 
                  style={{ marginBottom: lineIndex < section.content.length - 1 ? '8px' : 0 }}
                  dangerouslySetInnerHTML={{ __html: formattedLine }}
                />
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

const TicketDisplay = () => {
  const [tickets, setTickets] = useState([]);
  const [itineraries, setItineraries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sortColumn, setSortColumn] = useState();
  const [sortType, setSortType] = useState();
  const [searchKeyword, setSearchKeyword] = useState('');
  const [page, setPage] = useState(1);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filteredData, setFilteredData] = useState([]);
  const [orchestratorLoading, setOrchestratorLoading] = useState(false);
  const [pnrDetails, setPnrDetails] = useState(null);
  const [showPnrDetails, setShowPnrDetails] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState('idle'); // 'idle', 'verifying', 'verified', 'failed'
  const [verificationMessage, setVerificationMessage] = useState('');
  const [verificationSteps, setVerificationSteps] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [rawResponse, setRawResponse] = useState("");
  const [viewMode, setViewMode] = useState('structured');
  const [refundDetails, setRefundDetails] = useState(null);
  const [showRefundDetails, setShowRefundDetails] = useState(false);
  const [refundProcessing, setRefundProcessing] = useState(false);
  const [refundResult, setRefundResult] = useState(null); // 'success', 'error', or null

  // Add debugging for initial data loading
  useEffect(() => {
    console.log("Initial tickets state:", tickets);
    console.log("Initial itineraries state:", itineraries);
  }, [tickets, itineraries]);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      console.log("Fetching data from API...");
      const response = await fetch(`${API_URL}/refund/excel-data`);
      
      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }
      
      const data = await response.json();
      console.log("API response data:", data);

      if (!data.refundRules || !data.itineraries) {
        console.warn("Missing data in API response:", data);
        setTickets([]);
        setItineraries([]);
        setLoading(false);
        return;
      }

      const transformedTickets = data.refundRules.map(rule => {
        const ticket = {
          pnr: rule.bookingReference,
          passengerName: rule.passengerName,
          fareAmount: rule.fareAmount || 0,
          status: rule.status === "1" ? "COMPLETED" : rule.status === "2" ? "CANCELLED" : "PENDING",
          refundEligible: rule.amount > 0,
          refundAmount: rule.amount || 0,
          reason: rule.reason || "N/A",
          travelledHops: getCompletedHops(rule.bookingReference, data.itineraries),
          totalHops: getTotalHops(rule.bookingReference, data.itineraries)
        };
        console.log("Transformed ticket:", ticket);
        return ticket;
      });

      console.log("Setting tickets state with:", transformedTickets);
      console.log("Setting itineraries state with:", data.itineraries);
      
      setTickets(transformedTickets);
      setItineraries(data.itineraries);
      setFilteredData(transformedTickets);
    } catch (error) {
      console.error("Error fetching data:", error);
      // Show user-friendly error message
      toast.error("Failed to load ticket data. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const getCompletedHops = (pnr, itineraryList) => {
    if (!pnr || !itineraryList || !Array.isArray(itineraryList)) {
      console.warn("Invalid arguments to getCompletedHops:", { pnr, itineraryList });
      return 0;
    }
    
    const relevantItineraries = itineraryList.filter(i => i.bookingReference === pnr);
    console.log(`Found ${relevantItineraries.length} itineraries for PNR ${pnr}`);
    
    return relevantItineraries.filter(i => i.status === "COMPLETED").length;
  };

  const getTotalHops = (pnr, itineraryList) => {
    if (!pnr || !itineraryList || !Array.isArray(itineraryList)) {
      console.warn("Invalid arguments to getTotalHops:", { pnr, itineraryList });
      return 0;
    }
    
    const count = itineraryList.filter(i => i.bookingReference === pnr).length;
    console.log(`Total hops for PNR ${pnr}: ${count}`);
    return count;
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setSortColumn(sortColumn);
    setSortType(sortType);
    const sorted = [...tickets].sort((a, b) => {
      let x = a[sortColumn];
      let y = b[sortColumn];
      if (typeof x === 'string') x = x.toLowerCase();
      if (typeof y === 'string') y = y.toLowerCase();
      return sortType === 'asc' ? (x > y ? 1 : -1) : (x < y ? 1 : -1);
    });
    setTickets(sorted);
  };

  const handleSearch = async () => {
    if (!searchKeyword.trim()) return;

    try {
      const response = await fetch(`${API_URL}/pnr-detail/${searchKeyword.trim()}`);
      if (!response.ok) throw new Error('PNR not found');

      const data = await response.json();

      const formatted = {
        pnr: data.bookingReference,
        passengerName: data.passengerName,
        fareAmount: data.fareAmount || 0,
        status: data.status === "1" ? "COMPLETED" : data.status === "2" ? "CANCELLED" : "PENDING",
        refundEligible: data.amount > 0,
        refundAmount: data.amount,
        reason: data.reason,
        travelledHops: getCompletedHops(data.bookingReference, itineraries),
        totalHops: getTotalHops(data.bookingReference, itineraries)
      };

      setSelectedTicket(formatted);
      setShowDetails(true);
    } catch (error) {
      console.error("Search error:", error);
      alert("PNR not found or API error.");
    }
  };

  // Improve the filter logic
  useEffect(() => {
    if (!tickets || tickets.length === 0) {
      setFilteredData([]);
      return;
    }

    console.log("Applying filters - Status:", filterStatus, "Search:", searchKeyword);
    
    let filtered = [...tickets];
    
    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(ticket => 
        ticket.status.toLowerCase() === filterStatus.toLowerCase()
      );
    }
    
    // Apply search filter if there's a search keyword
    if (searchKeyword && searchKeyword.trim() !== '') {
      const keyword = searchKeyword.trim().toLowerCase();
      filtered = filtered.filter(ticket => 
        ticket.pnr.toLowerCase().includes(keyword) || 
        ticket.passengerName.toLowerCase().includes(keyword)
      );
    }
    
    console.log(`Filtered from ${tickets.length} to ${filtered.length} tickets`);
    setFilteredData(filtered);
    // Reset to first page when filters change
    setPage(1);
  }, [tickets, filterStatus, searchKeyword]);

  const exportToCSV = () => {
    const headers = Object.keys(tickets[0]).join(',');
    const rows = tickets.map(ticket => Object.values(ticket).join(',')).join('\n');
    const csv = `${headers}\n${rows}`;
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'tickets.csv';
    a.click();
  };

  const getItinerariesForPNR = (pnr) => {
    return itineraries.filter(i => i.bookingReference === pnr);
  };

  // Ensure pagination works correctly
  const limit = 10;
  const totalPages = Math.ceil((filteredData?.length || 0) / limit);
  const paginatedData = filteredData?.slice((page - 1) * limit, page * limit) || [];

  // Add effect to log filtered data changes
  useEffect(() => {
    console.log("Filtered data updated:", filteredData);
    console.log("Current page data:", paginatedData);
  }, [filteredData, page]);

  const fetchPnrDetailsFromOrchestrator = async (pnr) => {
    if (!pnr) return;
    
    setOrchestratorLoading(true);
    try {
      // Use the callOrchestrator function from api.js
      const requestData = {
        type: "pnr_search",
        rloc: pnr
      };
      
      const result = await callOrchestrator(JSON.stringify(requestData));
      console.log("Orchestrator PNR search response:", result);
      
      // Store the raw response
      if (result && result.message) {
        setRawResponse(result.message);
        console.log("Raw response set:", result.message);
      }
      
      // Check if we got a valid response
      if (result && typeof result === 'object') {
        // Parse the text response
        const parsedData = parseOrchestratorResponse(result.message, pnr);
        
        if (parsedData.error) {
          // Handle error case
          toast.warning(parsedData.errorMessage || "Unable to retrieve PNR details");
          setVerificationStatus('failed');
          setVerificationMessage(parsedData.errorMessage || "PNR verification failed. Please try again later.");
        } else {
          setPnrDetails(parsedData);
          setShowPnrDetails(true);
        }
      } else if (result?.type === "pnr_details" && result?.data) {
        // Handle structured JSON response if available
        setPnrDetails(result.data);
        setShowPnrDetails(true);
      } else {
        toast.warning("No detailed PNR information found");
      }
    } catch (error) {
      console.error("Error fetching PNR details from orchestrator:", error);
      toast.error("Failed to retrieve detailed PNR information");
    } finally {
      setOrchestratorLoading(false);
    }
  };

  // Function to parse the text response from the orchestrator
  const parseOrchestratorResponse = (text, pnr) => {
    // Check for error messages first
    if (text.includes("unable to retrieve") || 
        text.includes("issue with the PNR search") || 
        text.includes("apologize for the inconvenience")) {
      return {
        bookingReference: pnr,
        error: true,
        errorMessage: text
      };
    }

    // Initialize with default structure
    const parsedData = {
      bookingReference: pnr || extractBookingReference(text),
      customerDetails: [],
      segmentDetails: [],
      totalPrice: { monetaryAmount: null, baseCurrency: '£' },
      additionalInfo: {}, // Container for any new properties
      seatAssignments: {},
      ticketNumbers: {}
    };

    // Function to extract booking reference from text
    function extractBookingReference(text) {
      const bookingRefMatch = text.match(/\*\*Booking Reference:\*\*\s*([A-Z0-9]+)/i);
      return bookingRefMatch ? bookingRefMatch[1] : null;
    }

    // Generic section extractor that's more flexible with emoji markers
    const extractSections = (text) => {
      // Match any section that starts with an emoji followed by a title in bold
      const sectionRegex = /([\p{Emoji}\p{Emoji_Presentation}🛫🔍🎫🧳💸👤📅✈️🏨🚗📱🔄ℹ️👥💳🍽️🛄🔗])\s*\*\*([^*:]+):\*\*([\s\S]*?)(?=[\p{Emoji}\p{Emoji_Presentation}🛫🔍🎫🧳💸👤📅✈️🏨🚗📱🔄ℹ️👥💳🍽️🛄🔗]\s*\*\*|$)/gu;
      
      const sections = {};
      let match;
      
      while ((match = sectionRegex.exec(text)) !== null) {
        const emoji = match[1];
        const title = match[2].trim();
        const content = match[3].trim();
        sections[title] = { emoji, content };
      }
      
      return sections;
    };

    // Generic property extractor
    const extractProperty = (text, propertyName) => {
      const regex = new RegExp(`\\*\\*${propertyName}:\\*\\*\\s*(.*?)(?:\\n|$)`, 'i');
      const match = text.match(regex);
      return match ? match[1].trim() : null;
    };

    // Extract all sections with emoji markers
    const sections = extractSections(text);
    console.log("Extracted sections:", Object.keys(sections));

    // Process Seat Assignments section
    if (sections['Seat Assignments']) {
      const seatContent = sections['Seat Assignments'].content;
      const seatLines = seatContent.split('\n').map(line => line.trim()).filter(Boolean);
      
      seatLines.forEach(line => {
        const seatMatch = line.match(/-\s*(.*?):\s*(\d+[A-Z])/);
        if (seatMatch) {
          const [_, passengerName, seatNumber] = seatMatch;
          parsedData.seatAssignments[passengerName.trim()] = seatNumber.trim();
        }
      });
    }

    // Process Ticket Numbers section
    if (sections['Ticket Numbers']) {
      const ticketContent = sections['Ticket Numbers'].content;
      const ticketLines = ticketContent.split('\n').map(line => line.trim()).filter(Boolean);
      
      ticketLines.forEach(line => {
        const ticketMatch = line.match(/-\s*(.*?):\s*(\d+)/);
        if (ticketMatch) {
          const [_, passengerName, ticketNumber] = ticketMatch;
          parsedData.ticketNumbers[passengerName.trim()] = ticketNumber.trim();
        }
      });
    }

    // Process Booking Date
    if (sections['Booking Date']) {
      const bookingDateContent = sections['Booking Date'].content.trim();
      parsedData.bookingDate = bookingDateContent;
    }

    // Process Booking Reference
    if (sections['Booking Reference']) {
      const bookingRefContent = sections['Booking Reference'].content.trim();
      parsedData.bookingReference = bookingRefContent;
    }

    // Process Additional Services
    if (sections['Additional Services']) {
      const servicesContent = sections['Additional Services'].content;
      const servicesList = servicesContent.split(',').map(service => service.trim());
      parsedData.additionalServices = servicesList;
    }

    // Process Flight Details
    if (sections['Flight Details']) {
      const flightContent = sections['Flight Details'].content;
      
      // Extract airport codes from parentheses
      const originMatch = flightContent.match(/Origin.*?\(([A-Z]{3})\)/i);
      const destMatch = flightContent.match(/Destination.*?\(([A-Z]{3})\)/i);
      const airlineMatch = flightContent.match(/Airline.*?\(([A-Z]{2})\)/i);
      
      const segment = {
        originAirportCode: originMatch ? originMatch[1] : extractProperty(flightContent, 'Origin')?.substring(0, 3) || '',
        destinationAirportCode: destMatch ? destMatch[1] : extractProperty(flightContent, 'Destination')?.substring(0, 3) || '',
        marketingAirline: {
          name: extractProperty(flightContent, 'Airline')?.split('(')[0]?.trim() || 'Virgin Atlantic',
          code: airlineMatch ? airlineMatch[1] : 'VS',
          flightNumber: extractProperty(flightContent, 'Flight Number') || ''
        },
        flightDuration: extractProperty(flightContent, 'Duration') || '',
        scheduledDepartureDate: extractProperty(flightContent, 'Departure Date') || '',
        scheduledDepartureTime: extractProperty(flightContent, 'Departure Time') || '',
        scheduledArrivalTime: extractProperty(flightContent, 'Arrival Time') || '',
        cabinType: {
          cabinName: extractProperty(flightContent, 'Cabin Class') || ''
        },
        aircraft: extractProperty(flightContent, 'Aircraft') || '',
        status: extractProperty(flightContent, 'Status') || ''
      };
      
      // Add any other properties found in the flight section
      const knownProps = ['Origin', 'Destination', 'Airline', 'Flight Number', 'Duration', 
                          'Departure Date', 'Departure Time', 'Arrival Time', 'Cabin Class',
                          'Aircraft', 'Status'];
      
      const lines = flightContent.split('\n').map(line => line.trim()).filter(Boolean);
      
      lines.forEach(line => {
        const propMatch = line.match(/\*\*(.*?):\*\*\s*(.*)/);
        if (propMatch) {
          const [_, propName, propValue] = propMatch;
          if (!knownProps.includes(propName)) {
            segment[propName.toLowerCase().replace(/\s+/g, '_')] = propValue;
          }
        }
      });
      
      parsedData.segmentDetails.push(segment);
    }

    // Process Passengers section (new format)
    if (sections['Passengers']) {
      const passengerContent = sections['Passengers'].content;
      // Split by numbered entries (1., 2., etc.)
      const passengerBlocks = passengerContent.split(/\d+\.\s*\*\*/).filter(Boolean);
      
      passengerBlocks.forEach(block => {
        const nameMatch = block.match(/^(.*?)\*\*/);
        if (!nameMatch) return;
        
        const fullName = nameMatch[1].trim();
        const nameParts = fullName.split(' ');
        const lastName = nameParts.pop() || '';
        const firstName = nameParts.join(' ');
        
        // Extract passenger type with code in parentheses
        const typeMatch = block.match(/\*\*Type:\*\*\s*(.*?)\(([A-Z]{3})\)/i);
        
        const passenger = {
          firstName,
          lastName,
          gender: extractProperty(block, 'Gender')?.startsWith('M') ? 'M' : 'F',
          dateOfBirth: extractProperty(block, 'Date of Birth') || '',
          passengerType: typeMatch ? typeMatch[1].trim() : extractProperty(block, 'Type') || 'Adult',
          passengerTypeCode: typeMatch ? typeMatch[2] : '',
          contacts: {},
          seat: extractProperty(block, 'Seat') || '',
          ticketNumber: extractProperty(block, 'Ticket Number') || ''
        };
        
        // Extract contact information
        const contactPhone = extractProperty(block, 'Contact');
        if (contactPhone) {
          passenger.contacts.phone = contactPhone;
        }
        
        // Extract any other properties dynamically
        const knownProps = ['Type', 'Date of Birth', 'Gender', 'Contact', 'Seat', 'Ticket Number'];
        const lines = block.split('\n').map(line => line.trim()).filter(Boolean);
        
        lines.forEach(line => {
          const propMatch = line.match(/\*\*(.*?):\*\*\s*(.*)/);
          if (propMatch) {
            const [_, propName, propValue] = propMatch;
            if (!knownProps.includes(propName)) {
              passenger[propName.toLowerCase().replace(/\s+/g, '_')] = propValue;
            }
          }
        });
        
        parsedData.customerDetails.push(passenger);
      });
    }

    // Process Baggage Allowance
    if (sections['Baggage Allowance']) {
      const baggageContent = sections['Baggage Allowance'].content;
      parsedData.baggageAllowance = {};
      
      const lines = baggageContent.split('\n').map(line => line.trim()).filter(Boolean);
      lines.forEach(line => {
        const propMatch = line.match(/\*\*(.*?):\*\*\s*(.*)/);
        if (propMatch) {
          const [_, type, allowance] = propMatch;
          parsedData.baggageAllowance[type.toLowerCase().replace(/\s+/g, '_')] = allowance;
        } else {
          const dashMatch = line.match(/-\s*\*\*(.*?):\*\*\s*(.*)/);
          if (dashMatch) {
            const [_, type, allowance] = dashMatch;
            parsedData.baggageAllowance[type.toLowerCase().replace(/\s+/g, '_')] = allowance;
          } else {
            const simpleDashMatch = line.match(/-\s*(.*?):\s*(.*)/);
            if (simpleDashMatch) {
              const [_, type, allowance] = simpleDashMatch;
              parsedData.baggageAllowance[type.toLowerCase().replace(/\s+/g, '_')] = allowance;
            }
          }
        }
      });
    }

    // Process Fare and Payment Information section
    if (sections['Fare and Payment Information']) {
      const fareContent = sections['Fare and Payment Information'].content;
      parsedData.fareDetails = {};
      
      // Extract fare amount
      const fareMatch = fareContent.match(/\*\*Fare:\*\*\s*\$?(\d+(\.\d+)?)\s*(USD|GBP|EUR)?/i);
      if (fareMatch) {
        parsedData.fareDetails.baseFare = {
          amount: fareMatch[1],
          currency: fareMatch[3] || 'USD'
        };
      }
      
      // Extract taxes and fees
      const taxesMatch = fareContent.match(/\*\*Taxes and Fees:\*\*\s*\$?(\d+(\.\d+)?)\s*(USD|GBP|EUR)?/i);
      if (taxesMatch) {
        parsedData.fareDetails.taxesAndFees = {
          amount: taxesMatch[1],
          currency: taxesMatch[3] || 'USD'
        };
      }
      
      // Extract total price
      const totalPriceMatch = fareContent.match(/\*\*Total Price:\*\*\s*\$?(\d+(\.\d+)?)\s*(USD|GBP|EUR)?/i);
      if (totalPriceMatch) {
        parsedData.totalPrice = {
          monetaryAmount: totalPriceMatch[1],
          baseCurrency: totalPriceMatch[3] || 'USD'
        };
      }
      
      // Extract payment method
      const paymentMethod = extractProperty(fareContent, 'Payment Method');
      if (paymentMethod) {
        parsedData.paymentMethod = paymentMethod;
      }
      
      // Extract payment reference ID
      const paymentRefId = extractProperty(fareContent, 'Payment Reference ID');
      if (paymentRefId) {
        parsedData.paymentReferenceId = paymentRefId;
      }
    }

    // Merge seat assignments and ticket numbers into passenger details
    if (parsedData.customerDetails.length > 0) {
      parsedData.customerDetails.forEach(passenger => {
        const fullName = `${passenger.firstName} ${passenger.lastName}`;
        
        // Add seat assignment if available
        if (parsedData.seatAssignments[fullName]) {
          passenger.seat = parsedData.seatAssignments[fullName];
        }
        
        // Add ticket number if available
        if (parsedData.ticketNumbers[fullName]) {
          passenger.ticketNumber = parsedData.ticketNumbers[fullName];
        }
      });
    }

    // Add debug logging
    console.log("Parsed PNR data:", parsedData);
    
    return parsedData;
  };

  const verifyPnr = async (pnr) => {
    if (!pnr) return;
    
    // Define steps first
    const steps = [
      { id: 1, message: "Connecting to verification service...", complete: false },
      { id: 2, message: "Authenticating PNR request...", complete: false },
      { id: 3, message: "Searching for PNR in database...", complete: false },
      { id: 4, message: "Validating passenger information...", complete: false },
      { id: 5, message: "Retrieving detailed flight information...", complete: false }
    ];
    
    // Reset verification state
    setVerificationStatus('verifying');
    setVerificationMessage('Initiating verification process...');
    setVerificationSteps(steps);
    setCurrentStep(0);
    
    try {
      // Simulate a streaming workflow with steps
      for (let i = 0; i < steps.length; i++) {
        setCurrentStep(i);
        // Update the current step message using the local steps array
        setVerificationMessage(steps[i].message);
        
        // Wait for a random time between 800ms and 1500ms
        await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 700));
        
        // Mark the step as complete
        setVerificationSteps(prev => {
          const updated = [...prev];
          updated[i] = { ...updated[i], complete: true };
          return updated;
        });
      }
      
      // Set verification as complete
      setVerificationStatus('verified');
      setVerificationMessage('PNR verification complete! Retrieving detailed itinerary...');
      
      // Fetch PNR details after verification is complete
      fetchPnrDetailsFromOrchestrator(pnr);
    } catch (error) {
      console.error("Error verifying PNR:", error);
      setVerificationStatus('failed');
      setVerificationMessage('PNR verification failed. Please try again later.');
    }
  };

  // Add the mapOrchestratorDataToTicket function here
  const mapOrchestratorDataToTicket = (pnrDetails) => {
    if (!pnrDetails || !pnrDetails.segmentDetails || pnrDetails.segmentDetails.length === 0) {
      return null;
    }
    
    // Get the first segment for the ticket display
    const segment = pnrDetails.segmentDetails[0];
    
    // Format date for display (YYYY-MM-DD)
    const formatDate = (dateStr) => {
      if (!dateStr) return '';
      // Try to handle different date formats
      let date;
      if (dateStr.includes('/')) {
        // Handle DD/MM/YYYY format
        const parts = dateStr.split('/');
        date = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
      } else {
        date = new Date(dateStr);
      }
      
      if (isNaN(date.getTime())) return dateStr; // Return original if invalid
      return date.toISOString().split('T')[0]; // YYYY-MM-DD
    };
    
    // Get passenger name
    const passengerName = pnrDetails.customerDetails && pnrDetails.customerDetails.length > 0 
      ? `${pnrDetails.customerDetails[0].firstName || ''} ${pnrDetails.customerDetails[0].lastName || ''}`.trim() || 'Passenger'
      : 'Passenger';
    
    // Get seat assignment
    const seat = pnrDetails.seatAssignments && Object.keys(pnrDetails.seatAssignments).length > 0
      ? pnrDetails.seatAssignments[Object.keys(pnrDetails.seatAssignments)[0]]
      : 'TBA';
    
    // Map to ticket format
    return {
      flightNumber: segment.marketingAirline?.flightNumber || 'VS000',
      departureCode: segment.originAirportCode || 'DEP',
      departureName: segment.originAirportName || segment.originAirportCode || 'Departure',
      departureTime: segment.scheduledDepartureTime || '00:00',
      departureDate: formatDate(segment.scheduledDepartureDate),
      arrivalCode: segment.destinationAirportCode || 'ARR',
      arrivalName: segment.destinationAirportName || segment.destinationAirportCode || 'Arrival',
      arrivalTime: segment.scheduledArrivalTime || '00:00',
      arrivalDate: formatDate(segment.scheduledArrivalDate || segment.scheduledDepartureDate),
      passengerName: passengerName,
      ticketClass: segment.cabinType?.cabinName || 'Economy',
      seat: seat,
      gate: segment.departureGate || 'TBA',
      boardingTime: segment.boardingTime || 
                   (segment.scheduledDepartureTime ? 
                    calculateBoardingTime(segment.scheduledDepartureTime) : 
                    'TBA'),
      price: pnrDetails.totalPrice?.monetaryAmount ? 
             `${pnrDetails.totalPrice.baseCurrency || '£'}${pnrDetails.totalPrice.monetaryAmount}` : 
             'N/A',
      status: segment.status || 'Confirmed',
      pnr: pnrDetails.bookingReference || 'XXXXXX',
      miles: calculateMiles(segment.originAirportCode, segment.destinationAirportCode)
    };
  };

  // Helper function to calculate boarding time (30 mins before departure)
  const calculateBoardingTime = (departureTime) => {
    if (!departureTime) return 'TBA';
    
    try {
      // Parse time in HH:MM format
      const [hours, minutes] = departureTime.split(':').map(Number);
      
      // Calculate boarding time (30 mins before)
      let boardingHours = hours;
      let boardingMinutes = minutes - 30;
      
      // Handle minute underflow
      if (boardingMinutes < 0) {
        boardingMinutes += 60;
        boardingHours -= 1;
      }
      
      // Handle hour underflow
      if (boardingHours < 0) {
        boardingHours += 24;
      }
      
      // Format as HH:MM
      return `${boardingHours.toString().padStart(2, '0')}:${boardingMinutes.toString().padStart(2, '0')}`;
    } catch (e) {
      console.error("Error calculating boarding time:", e);
      return 'TBA';
    }
  };

  // Helper function to calculate approximate miles based on airport codes
  const calculateMiles = (origin, destination) => {
    // This would ideally use a distance API or database
    // For now, return a realistic number based on common routes
    const commonRoutes = {
      'LHRJFK': '3,458',
      'LHRLAX': '5,456',
      'LHRDEL': '4,168',
      'LHRDXB': '3,400',
      'LHRHKG': '5,994',
      'JFKLHR': '3,458',
      'LAXLHR': '5,456',
      'DELLHR': '4,168',
      'DXBLHR': '3,400',
      'HKGLHR': '5,994',
    };
    
    const route = `${origin || ''}${destination || ''}`;
    return commonRoutes[route] || '2,500'; // Default fallback
  };

  const checkRefundEligibility = async (pnr) => {
    if (!pnr) {
      // Show error in modal instead of toast
      setRefundDetails({
        isEligible: false,
        refundAmount: 0,
        currency: "₹",
        reason: "PNR is required to check refund eligibility",
        deductions: [],
        processingTime: ""
      });
      setRefundResult('error');
      setShowRefundDetails(true);
      return;
    }

    // Show loading state and open modal immediately
    setRefundProcessing(true);
    setRefundResult(null);
    setRefundDetails(null);
    setShowRefundDetails(true);
    setOrchestratorLoading(true);

    try {
      // Parse the raw response into a structured JSON format
      const parsedPnrData = parseRawResponseToJson(rawResponse);

      if (!parsedPnrData) {
        throw new Error("Failed to parse PNR data");
      }

      // Enhance the request with tax details and segment information
      const requestData = {
        type: "refund_eligibility",
        data: {
          ...parsedPnrData,
          // Add detailed tax information
          taxDetails: {
            totalTaxAmount: parsedPnrData.payment?.taxesBreakdown?.reduce((sum, tax) => sum + (tax.amount || 0), 0) || 0,
            taxBreakdown: parsedPnrData.payment?.taxesBreakdown || [],
            baseFare: parsedPnrData.payment?.baseFare || 0,
            totalFare: parsedPnrData.payment?.totalAmount || 0,
            currency: parsedPnrData.payment?.currency || "₹"
          },
          // Add detailed segment information
          segmentDetails: parsedPnrData.segments?.map(segment => ({
            segmentNumber: segment.segmentNumber,
            origin: segment.origin,
            destination: segment.destination,
            airline: segment.airline,
            airlineCode: segment.airlineCode,
            flightNumber: segment.flightNumber,
            departureDate: segment.departure?.split(' ')[0] || '',
            departureTime: segment.departure?.split(' ')[1] || '',
            arrivalDate: segment.arrival?.split(' ')[0] || '',
            arrivalTime: segment.arrival?.split(' ')[1] || '',
            status: segment.status,
            class: segment.class,
            isFlown: segment.status === 'FLOWN' || segment.status === 'COMPLETED',
            isCancelled: segment.status === 'CANCELLED',
            isNoShow: segment.status === 'NO SHOW'
          })) || []
        }
      };

      // Log the request for debugging
      console.log("Sending refund eligibility request:", JSON.stringify(requestData, null, 2));

      // Call the orchestrator with the formatted request
      const result = await callOrchestrator(JSON.stringify(requestData));

      // Log the response for debugging
      console.log("Refund eligibility response:", result);

      if (!result) {
        throw new Error("No response received from orchestrator");
      }

      // Handle the response based on its type
      if (result.type === "refund_eligibility" && result.data) {
        // Extract refund details from the response
        const {
          isEligible,
          refundAmount,
          currency = "₹",
          reason,
          deductions = [],
          processingTime = "5-7 business days"
        } = result.data;

        // Update the UI with refund information
        setRefundDetails({
          isEligible,
          refundAmount,
          currency,
          reason,
          deductions,
          processingTime
        });

        // Set result status
        setRefundResult(isEligible ? 'success' : 'info');

      } else if (result.message) {
        // Handle text response
        setRefundDetails({
          isEligible: false,
          refundAmount: 0,
          currency: "₹",
          reason: result.message,
          deductions: [],
          processingTime: ""
        });
        setRefundResult('info');
      } else {
        throw new Error("Invalid response format from orchestrator");
      }
    } catch (error) {
      console.error("Error checking refund eligibility:", error);
      setRefundDetails({
        isEligible: false,
        refundAmount: 0,
        currency: "₹",
        reason: `Failed to check refund eligibility: ${error.message}`,
        deductions: [],
        processingTime: ""
      });
      setRefundResult('error');
    } finally {
      setRefundProcessing(false);
      setOrchestratorLoading(false);
    }
  };

  // Function to parse the raw text response into a structured JSON format
  const parseRawResponseToJson = (rawText) => {
    if (!rawText) return null;
    
    try {
      console.log("Parsing raw response:", rawText);
      
      // Initialize the JSON structure
      const parsedData = {
        bookingDetails: {
          pnr: extractValue(rawText, 'PNR') || extractValue(rawText, 'Booking Reference'),
          bookingDate: extractValue(rawText, 'Booking Date')
        },
        flightDetails: {
          origin: extractValue(rawText, 'Origin'),
          destination: extractValue(rawText, 'Destination'),
          airline: extractValue(rawText, 'Airline')?.split('(')[0]?.trim(),
          airlineCode: extractValue(rawText, 'Airline')?.match(/\(([A-Z]+)\)/)?.[1],
          flightNumber: extractValue(rawText, 'Flight Number'),
          departureDate: extractValue(rawText, 'Departure Date'),
          departureTime: extractValue(rawText, 'Departure Time'),
          arrivalTime: extractValue(rawText, 'Arrival Time'),
          duration: extractValue(rawText, 'Duration'),
          aircraft: extractValue(rawText, 'Aircraft'),
          cabin: extractValue(rawText, 'Cabin'),
          status: extractValue(rawText, 'Status')
        },
        passenger: {
          name: extractValue(rawText, 'Passenger') || extractValue(rawText, 'Name'),
          type: extractValue(rawText, 'Type', 'Passenger Information'),
          dateOfBirth: extractValue(rawText, 'Date of Birth', 'Passenger Information'),
          gender: extractValue(rawText, 'Gender', 'Passenger Information'),
          contact: extractValue(rawText, 'Contact', 'Passenger Information'),
          ticketNumber: extractValue(rawText, 'Ticket Number', 'Passenger Information')
        },
        segments: extractSegments(rawText),
        payment: {
          totalAmount: extractNumericValue(rawText, 'Total Amount', 'Payment Information') || 
                       extractNumericValue(rawText, 'Total Price'),
          currency: extractCurrency(rawText, 'Total Amount', 'Payment Information') || 
                    extractCurrency(rawText, 'Total Price') || 'GBP',
          method: extractValue(rawText, 'Payment Method', 'Payment Information'),
          baseFare: extractNumericValue(rawText, 'Base Fare', 'Payment Information') || 
                    extractNumericValue(rawText, 'Fare'),
          taxesBreakdown: extractTaxesBreakdown(rawText)
        },
        baggageAllowance: {
          cabinBaggage: extractValue(rawText, 'Cabin Baggage', 'Baggage Allowance'),
          checkedBaggage: extractValue(rawText, 'Checked Baggage', 'Baggage Allowance'),
          priorityHandling: extractValue(rawText, 'Priority Baggage Handling', 'Baggage Allowance') ? true : false
        },
        services: {
          meals: extractListValue(rawText, 'Meals', 'Services'),
          seating: extractListValue(rawText, 'Seating', 'Services'),
          otherServices: extractListValue(rawText, 'Other Services', 'Services')
        }
      };
      
      // If no segments were found, create one from flight details
      if (parsedData.segments.length === 0 && parsedData.flightDetails.flightNumber) {
        parsedData.segments = [{
          segmentNumber: 1,
          origin: parsedData.flightDetails.origin,
          destination: parsedData.flightDetails.destination,
          airline: parsedData.flightDetails.airline,
          airlineCode: parsedData.flightDetails.airlineCode,
          flightNumber: parsedData.flightDetails.flightNumber,
          departure: `${parsedData.flightDetails.departureDate} ${parsedData.flightDetails.departureTime}`,
          arrival: `${parsedData.flightDetails.departureDate} ${parsedData.flightDetails.arrivalTime}`,
          duration: parsedData.flightDetails.duration,
          aircraft: parsedData.flightDetails.aircraft,
          class: parsedData.flightDetails.cabin,
          status: parsedData.flightDetails.status
        }];
      }
      
      // If no tax breakdown was found but we have base fare and total amount, calculate tax
      if (parsedData.payment.taxesBreakdown.length === 0 && 
          parsedData.payment.baseFare && 
          parsedData.payment.totalAmount) {
        const taxAmount = parsedData.payment.totalAmount - parsedData.payment.baseFare;
        if (taxAmount > 0) {
          parsedData.payment.taxesBreakdown = [{
            code: "TX",
            amount: taxAmount,
            currency: parsedData.payment.currency,
            description: "Total Taxes"
          }];
        }
      }
      
      // Clean up the object by removing null/undefined values
      const cleanedData = cleanObject(parsedData);
      console.log("Parsed PNR data:", cleanedData);
      return cleanedData;
    } catch (error) {
      console.error("Error parsing raw response:", error);
      return null;
    }
  };

  // Enhanced function to extract segments from the raw text
  const extractSegments = (text) => {
    const segments = [];
    
    // Try multiple patterns to find flight segments
    // Pattern 1: Flight Segment sections
    const segmentPattern1 = /\*\*Flight Segment (\d+):\*\*\s*([\s\S]*?)(?=\*\*Flight Segment|\*\*Payment Information|\*\*Baggage Allowance|$)/g;
    let match;
    
    while ((match = segmentPattern1.exec(text)) !== null) {
      const segmentNumber = match[1];
      const segmentContent = match[2];
      
      const segment = {
        segmentNumber: parseInt(segmentNumber),
        origin: extractValue(segmentContent, 'Origin'),
        destination: extractValue(segmentContent, 'Destination'),
        airline: extractValue(segmentContent, 'Airline')?.split('(')[0]?.trim(),
        airlineCode: extractValue(segmentContent, 'Airline')?.match(/\(([A-Z]+)\)/)?.[1],
        flightNumber: extractValue(segmentContent, 'Flight Number'),
        aircraft: extractValue(segmentContent, 'Aircraft'),
        class: extractValue(segmentContent, 'Class') || extractValue(segmentContent, 'Cabin Class'),
        departure: extractValue(segmentContent, 'Departure') || 
                  `${extractValue(segmentContent, 'Departure Date')} ${extractValue(segmentContent, 'Departure Time')}`,
        arrival: extractValue(segmentContent, 'Arrival') || 
                `${extractValue(segmentContent, 'Arrival Date') || extractValue(segmentContent, 'Departure Date')} ${extractValue(segmentContent, 'Arrival Time')}`,
        duration: extractValue(segmentContent, 'Duration'),
        status: extractValue(segmentContent, 'Status')
      };
      
      segments.push(segment);
    }
    
    // If no segments found, try to extract from flight details section
    if (segments.length === 0) {
      const flightDetailsPattern = /\*\*Flight Details\*\*\s*([\s\S]*?)(?=\*\*Passenger Information|\*\*Payment Information|$)/i;
      const flightMatch = text.match(flightDetailsPattern);
      
      if (flightMatch && flightMatch[1]) {
        const flightContent = flightMatch[1];
        
        const segment = {
          segmentNumber: 1,
          origin: extractValue(flightContent, 'Origin'),
          destination: extractValue(flightContent, 'Destination'),
          airline: extractValue(flightContent, 'Airline')?.split('(')[0]?.trim(),
          airlineCode: extractValue(flightContent, 'Airline')?.match(/\(([A-Z]+)\)/)?.[1],
          flightNumber: extractValue(flightContent, 'Flight Number'),
          aircraft: extractValue(flightContent, 'Aircraft'),
          class: extractValue(flightContent, 'Cabin') || extractValue(flightContent, 'Class'),
          departure: `${extractValue(flightContent, 'Departure Date')} ${extractValue(flightContent, 'Departure Time')}`,
          arrival: `${extractValue(flightContent, 'Arrival Date') || extractValue(flightContent, 'Departure Date')} ${extractValue(flightContent, 'Arrival Time')}`,
          duration: extractValue(flightContent, 'Duration'),
          status: extractValue(flightContent, 'Status')
        };
        
        segments.push(segment);
      }
    }
    
    // If still no segments, create one from the flight details in the main data
    if (segments.length === 0 && extractValue(text, 'Flight Number')) {
      const segment = {
        segmentNumber: 1,
        origin: extractValue(text, 'Origin'),
        destination: extractValue(text, 'Destination'),
        airline: extractValue(text, 'Airline')?.split('(')[0]?.trim(),
        airlineCode: extractValue(text, 'Airline')?.match(/\(([A-Z]+)\)/)?.[1],
        flightNumber: extractValue(text, 'Flight Number'),
        aircraft: extractValue(text, 'Aircraft'),
        class: extractValue(text, 'Cabin') || extractValue(text, 'Class'),
        departure: `${extractValue(text, 'Departure Date')} ${extractValue(text, 'Departure Time')}`,
        arrival: `${extractValue(text, 'Arrival Date') || extractValue(text, 'Departure Date')} ${extractValue(text, 'Arrival Time')}`,
        duration: extractValue(text, 'Duration'),
        status: extractValue(text, 'Status')
      };
      
      segments.push(segment);
    }
    
    return segments;
  };

  // Enhanced function to extract taxes breakdown from the raw text
  const extractTaxesBreakdown = (text) => {
    const taxes = [];
    
    // Find the taxes breakdown section
    const taxesPattern = /\*\*Taxes Breakdown:\*\*\s*([\s\S]*?)(?=\*\*Baggage Allowance|\*\*Services|$)/;
    const taxesMatch = text.match(taxesPattern);
    
    if (taxesMatch && taxesMatch[1]) {
      const taxesContent = taxesMatch[1];
      
      // Extract each tax line - handle multiple formats
      // Format 1: - 123.45 GBP (Code: YQ)
      // Format 2: - YQ: 123.45 GBP
      // Format 3: - Airport Tax: 123.45 GBP
      const taxLinePattern = /-\s*(?:([A-Z0-9]+):\s*)?([0-9.]+)\s*([A-Z]+)(?:\s*\(Code:\s*([A-Z0-9]*)\))?|(?:([^:]+):\s*([0-9.]+)\s*([A-Z]+))/g;
      let taxMatch;
      
      while ((taxMatch = taxLinePattern.exec(taxesContent)) !== null) {
        let code, amount, currency, description;
        
        if (taxMatch[2] && taxMatch[3]) {
          // Format 1 or 2
          code = taxMatch[1] || taxMatch[4] || "TAX";
          amount = parseFloat(taxMatch[2]);
          currency = taxMatch[3];
          description = code;
        } else if (taxMatch[5] && taxMatch[6] && taxMatch[7]) {
          // Format 3
          description = taxMatch[5].trim();
          amount = parseFloat(taxMatch[6]);
          currency = taxMatch[7];
          code = description.split(' ')[0] || "TAX";
        }
        
        if (amount && currency) {
          taxes.push({
            code,
            amount,
            currency,
            description: description || code
          });
        }
      }
      
      // If no taxes found with regex, try a simpler approach
      if (taxes.length === 0) {
        // Look for lines with currency symbols and numbers
        const simplePattern = /-\s*([^:]+):\s*([£$€]?\s*[0-9,.]+)/g;
        while ((taxMatch = simplePattern.exec(taxesContent)) !== null) {
          const description = taxMatch[1].trim();
          const amountStr = taxMatch[2].replace(/[£$€\s]/g, '').replace(',', '');
          const amount = parseFloat(amountStr);
          
          if (!isNaN(amount)) {
            taxes.push({
              code: description.substring(0, 2).toUpperCase(),
              amount,
              currency: "GBP", // Default to GBP if not specified
              description
            });
          }
        }
      }
    }
    
    // If we still have no taxes but have a total amount, create a default tax entry
    if (taxes.length === 0) {
      const totalTaxMatch = text.match(/Total Tax(?:es)?:\s*([£$€]?\s*[0-9,.]+)\s*([A-Z]+)?/i);
      if (totalTaxMatch) {
        const amountStr = totalTaxMatch[1].replace(/[£$€\s]/g, '').replace(',', '');
        const amount = parseFloat(amountStr);
        const currency = totalTaxMatch[2] || "GBP";
        
        if (!isNaN(amount)) {
          taxes.push({
            code: "TX",
            amount,
            currency,
            description: "Total Taxes"
          });
        }
      }
    }
    
    return taxes;
  };

  // Helper function to extract a value from the raw text
  const extractValue = (text, key, section = null) => {
    if (!text) return null;
    
    // If a section is specified, first try to find the value within that section
    if (section) {
      const sectionPattern = new RegExp(`\\*\\*${section}:\\*\\*\\s*((?:[\\s\\S](?!\\*\\*[A-Za-z]+:\\*\\*))*)`);
      const sectionMatch = text.match(sectionPattern);
      
      if (sectionMatch) {
        const sectionText = sectionMatch[1];
        const keyPattern = new RegExp(`\\*\\*${key}:\\*\\*\\s*([^\\n]+)`, 'i');
        const keyMatch = sectionText.match(keyPattern);
        
        if (keyMatch) return keyMatch[1].trim();
      }
    }
    
    // Otherwise, look for the key anywhere in the text
    const pattern = new RegExp(`\\*\\*${key}:\\*\\*\\s*([^\\n]+)`, 'i');
    const match = text.match(pattern);
    
    return match ? match[1].trim() : null;
  };

  // Helper function to extract a list value from the raw text
  const extractListValue = (text, key, section = null) => {
    const value = extractValue(text, key, section);
    if (!value) return [];
    
    return value.split(',').map(item => item.trim());
  };

  // Helper function to extract numeric values
  const extractNumericValue = (text, key, section = null) => {
    const value = extractValue(text, key, section);
    if (!value) return 0;
    
    const numericMatch = value.match(/([0-9.]+)/);
    return numericMatch ? parseFloat(numericMatch[1]) : 0;
  };

  // Helper function to extract currency
  const extractCurrency = (text, key, section = null) => {
    const value = extractValue(text, key, section);
    if (!value) return null;
    
    const currencyMatch = value.match(/([0-9.]+)\s*([A-Z]{3})/);
    return currencyMatch ? currencyMatch[2] : null;
  };

  // Helper function to clean an object by removing null/undefined values
  const cleanObject = (obj) => {
    if (obj === null || typeof obj !== 'object') return obj;
    
    if (Array.isArray(obj)) {
      return obj.map(cleanObject).filter(item => item !== null && item !== undefined);
    }
    
    return Object.entries(obj).reduce((acc, [key, value]) => {
      const cleanedValue = cleanObject(value);
      
      // Only include non-null, non-undefined values
      // For objects, only include if they have at least one property
      if (cleanedValue !== null && cleanedValue !== undefined) {
        if (typeof cleanedValue !== 'object' || 
            Object.keys(cleanedValue).length > 0 || 
            Array.isArray(cleanedValue)) {
          acc[key] = cleanedValue;
        }
      }
      
      return acc;
    }, {});
  };

  // Helper function to extract airport codes from the route
  const extractAirportCode = (text, type) => {
    const routeMatch = text.match(/Route:\s*([A-Z]{3})\s*\([^)]+\)\s*[🛫→]\s*([A-Z]{3})\s*\([^)]+\)/i);
    if (routeMatch) {
      return type === 'origin' ? routeMatch[1] : routeMatch[2];
    }
    return null;
  };

  return (
    <div className="ticket-display-wrapper">
      <div className="ticket-display">
        {/* Document metadata tags */}
        <title>Ticket Management Dashboard</title>
        <meta name="description" content="Manage and track flight tickets, verify PNRs, and check refund eligibility" />
        <link rel="canonical" href="/tickets" />

        {/* When viewing a specific ticket */}
        {selectedTicket && (
          <>
            <title>Ticket {selectedTicket.pnr} - {selectedTicket.passengerName}</title>
            <meta name="description" content={`Ticket details for ${selectedTicket.passengerName}, status: ${selectedTicket.status}`} />
          </>
        )}

        {/* Add ToastContainer for notifications */}
        <ToastContainer position="top-right" autoClose={5000} />
      
      <div className="controls-container">
        <div className="search-filter">
          <InputGroup inside className="search-input">
            <Input
              placeholder="Search tickets or PNR..."
              value={searchKeyword}
              onChange={value => setSearchKeyword(value)}
              onPressEnter={handleSearch}
            />
            <InputGroup.Button onClick={handleSearch}>
              <Search size={20} />
            </InputGroup.Button>
          </InputGroup>

          <ButtonGroup className="filter-buttons">
            <Button appearance="ghost" className={filterStatus === 'all' ? 'active-filter' : ''} onClick={() => setFilterStatus('all')}>All</Button>
            <Button appearance="ghost" className={filterStatus === 'completed' ? 'active-filter' : ''} onClick={() => setFilterStatus('completed')}>Completed</Button>
            <Button appearance="ghost" className={filterStatus === 'cancelled' ? 'active-filter' : ''} onClick={() => setFilterStatus('cancelled')}>Cancelled</Button>
          </ButtonGroup>
        </div>

        <div className="actions">
          <Button appearance="primary" onClick={exportToCSV}><Download size={16} /> Export</Button>
          <Button appearance="subtle" onClick={() => window.location.reload()}><RefreshCw size={16} /></Button>
        </div>
      </div>

      <Table
        height={600}
        data={paginatedData}
        sortColumn={sortColumn}
        sortType={sortType}
        onSortColumn={handleSortColumn}
        loading={loading}
        onRowClick={data => {
          // Reset verification status
          setVerificationStatus('idle');
          setVerificationMessage('');

          // Set the selected ticket for the regular details modal
          setSelectedTicket(data);
          setShowDetails(true);

          // Start verification process
          verifyPnr(data.pnr);
        }}
        hover
        bordered
        cellBordered
        autoHeight
        wordWrap
        className="custom-table"
        style={{ width: '100%', minWidth: '800px' }}
      >
        <Column width={100} align="center" fixed resizable>
          <HeaderCell>PNR</HeaderCell>
          <Cell dataKey="pnr" />
        </Column>

        <Column width={200} resizable>
          <HeaderCell>Passenger Name</HeaderCell>
          <Cell dataKey="passengerName" />
        </Column>

        <Column width={140} align="right" resizable>
          <HeaderCell>Fare Amount</HeaderCell>
          <Cell dataKey="fareAmount">{rowData => `₹${rowData.fareAmount.toLocaleString()}`}</Cell>
        </Column>

        <Column width={120} resizable>
          <HeaderCell>Travelled/Total</HeaderCell>
          <Cell>{rowData => `${rowData.travelledHops}/${rowData.totalHops}`}</Cell>
        </Column>

        <Column width={140} resizable>
          <HeaderCell>Status</HeaderCell>
          <Cell>{rowData => <span className={`status-badge ${rowData.status.toLowerCase()}`}>{rowData.status}</span>}</Cell>
        </Column>

        <Column width={140} resizable>
          <HeaderCell>Refund Eligible</HeaderCell>
          <Cell>{rowData => <span className={`eligibility-badge ${rowData.refundEligible ? 'eligible' : 'not-eligible'}`}>{rowData.refundEligible ? 'YES' : 'NO'}</span>}</Cell>
        </Column>

        <Column width={140} align="right" resizable>
          <HeaderCell>Refund Amount</HeaderCell>
          <Cell>{rowData => `₹${rowData.refundAmount.toLocaleString()}`}</Cell>
        </Column>
      </Table>

      <div className="pagination-container">
        <Pagination
          prev
          next
          first
          last
          ellipsis
          boundaryLinks
          maxButtons={5}
          size="md"
          layout={['total', '-', 'limit', '|', 'pager', 'skip']}
          total={filteredData.length}
          limitOptions={[10, 20, 30]}
          limit={limit}
          activePage={page}
          onChangePage={setPage}
        />
      </div>

      <Modal open={showDetails} onClose={() => setShowDetails(false)} size="md">
        <Modal.Header>
          <Modal.Title>Ticket Details - {selectedTicket?.pnr}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedTicket && (
            <div className="ticket-details" style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
              <div className="ticket-info" style={{ 
                display: 'grid', 
                gridTemplateColumns: 'repeat(2, 1fr)', 
                gap: '15px',
                padding: '16px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px',
                border: '1px solid #e9ecef'
              }}>
                <div className="info-item">
                  <div className="info-label">Passenger Name</div>
                  <div className="info-value">{selectedTicket.passengerName}</div>
                </div>
                
                <div className="info-item">
                  <div className="info-label">PNR</div>
                  <div className="info-value">{selectedTicket.pnr}</div>
                </div>
                
                <div className="info-item">
                  <div className="info-label">Status</div>
                  <div className="info-value">
                    <span className={`status-badge status-${selectedTicket.status.toLowerCase()}`}>
                      {selectedTicket.status}
                    </span>
                  </div>
                </div>
                
                <div className="info-item">
                  <div className="info-label">Fare Amount</div>
                  <div className="info-value">₹{selectedTicket.fareAmount.toLocaleString()}</div>
                </div>
                
                {/* Removed the Travel Progress section */}
              </div>
              
              <div className="verification-section" style={{ 
                padding: '12px', 
                backgroundColor: '#f8f9fa', 
                borderRadius: '8px' 
              }}>
                <h4 style={{ fontSize: '16px', marginBottom: '8px' }}>PNR Verification</h4>
                
                <VerificationWorkflow 
                  status={verificationStatus}
                  currentStep={currentStep}
                  steps={verificationSteps}
                  orchestratorLoading={orchestratorLoading}
                />
                
                {verificationStatus === 'idle' && (
                  <div className="verification-actions" style={{ marginTop: '10px' }}>
                    <Button appearance="primary" onClick={() => verifyPnr(selectedTicket.pnr)} size="sm">
                      Verify PNR
                    </Button>
                  </div>
                )}
                
                {verificationStatus === 'failed' && (
                  <div className="verification-actions" style={{ marginTop: '10px' }}>
                    <Button appearance="primary" onClick={() => verifyPnr(selectedTicket.pnr)} size="sm">
                      Try Again
                    </Button>
                  </div>
                )}
                
                {verificationStatus === 'verified' && !orchestratorLoading && pnrDetails && (
                  <div className="verification-actions" style={{ 
                    marginTop: '10px',
                    display: 'flex',
                    gap: '8px',
                    flexWrap: 'wrap'
                  }}>
                    <Button 
                      appearance="primary" 
                      onClick={() => setShowPnrDetails(true)}
                      size="sm"
                    >
                      View Itinerary
                    </Button>
                    
                    <Button 
                      appearance="primary" 
                      color="green" 
                      onClick={() => checkRefundEligibility(selectedTicket.pnr)}
                      size="sm"
                    >
                      Check Refund
                    </Button>
                  </div>
                )}
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowDetails(false)} appearance="subtle" size="sm">Close</Button>
        </Modal.Footer>
      </Modal>

      {/* Add this new modal for PNR details from orchestrator */}
      <Modal open={showPnrDetails} onClose={() => setShowPnrDetails(false)} size="lg">
        <Modal.Header>
          <Modal.Title>
            Detailed Flight Itinerary - {pnrDetails?.bookingReference || selectedTicket?.pnr}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {orchestratorLoading ? (
            <div className="loading-container">
              <Loader size="md" content="Loading detailed PNR information..." />
            </div>
          ) : (
            <div>
              {/* 3D Ticket Display */}
              {pnrDetails && (
                <div className="premium-ticket-container">
                  <AirlineTicket 
                    ticketData={mapOrchestratorDataToTicket(pnrDetails)} 
                    className="modal-ticket"
                  />
                </div>
              )}
              
              {/* View mode selector */}
              <div style={{ marginBottom: '20px' }}>
                <ButtonGroup>
                  {/* <Button 
                    appearance={viewMode === 'structured' ? 'primary' : 'ghost'} 
                    onClick={() => setViewMode('structured')}
                  >
                    Structured View
                  </Button> */}
                  <Button 
                    appearance={viewMode === 'formatted' ? 'primary' : 'ghost'} 
                    onClick={() => setViewMode('formatted')}
                  >
                    Formatted View
                  </Button>
                  <Button 
                    appearance={viewMode === 'raw' ? 'primary' : 'ghost'} 
                    onClick={() => setViewMode('raw')}
                  >
                    Raw View
                  </Button>
                </ButtonGroup>
              </div>
              
              {/* Content based on view mode */}
              {viewMode === 'raw' && (
                <div style={{ 
                  backgroundColor: '#f5f5f5', 
                  padding: '15px', 
                  borderRadius: '4px',
                  maxHeight: '500px',
                  overflowY: 'auto'
                }}>
                  <pre style={{ 
                    whiteSpace: 'pre-wrap', 
                    fontFamily: 'monospace',
                    margin: 0
                  }}>
                    {rawResponse}
                  </pre>
                </div>
              )}
              
              {viewMode === 'formatted' && (
                <FormattedResponse text={rawResponse} />
              )}
              
              {viewMode === 'structured' && pnrDetails && (
                <div className="pnr-details-container">
                  {/* Customer Details Section */}
                  <div className="pnr-section">
                    <h5 className="section-title">Passenger Information</h5>
                    <div className="customer-details">
                      {pnrDetails.customerDetails && pnrDetails.customerDetails.length > 0 ? (
                        <div className="customer-list">
                          {pnrDetails.customerDetails.map((customer, idx) => (
                            <div key={idx} className="customer-card">
                              <div className="customer-name">
                                {customer.firstName || ''} {customer.lastName || ''}
                              </div>
                              <div className="customer-info-grid">
                                {customer.dateOfBirth && (
                                  <div className="info-item">
                                    <span className="info-label">Date of Birth:</span>
                                    <span className="info-value">{customer.dateOfBirth}</span>
                                  </div>
                                )}
                                {customer.gender && (
                                  <div className="info-item">
                                    <span className="info-label">Gender:</span>
                                    <span className="info-value">
                                      {customer.gender === 'M' ? 'Male' : 
                                       customer.gender === 'F' ? 'Female' : customer.gender}
                                    </span>
                                  </div>
                                )}
                                {customer.custId && (
                                  <div className="info-item">
                                    <span className="info-label">Customer ID:</span>
                                    <span className="info-value">{customer.custId}</span>
                                  </div>
                                )}
                                {customer.contacts?.phone && (
                                  <div className="info-item">
                                    <span className="info-label">Phone:</span>
                                    <span className="info-value">{customer.contacts.phone}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="no-data">No passenger information available</div>
                      )}
                    </div>
                  </div>
                  
                  {/* Flight Segments Section */}
                  <div className="pnr-section">
                    <h5 className="section-title">Flight Details</h5>
                    <div className="segment-details">
                      {pnrDetails.segmentDetails && pnrDetails.segmentDetails.length > 0 ? (
                        <div className="segment-list">
                          {pnrDetails.segmentDetails.map((segment, idx) => (
                            <div key={idx} className="segment-detail-card">
                              <div className="segment-header">
                                <div className="segment-route">
                                  <span className="origin">{segment.originAirportCode || 'N/A'}</span>
                                  <span className="route-arrow">→</span>
                                  <span className="destination">{segment.destinationAirportCode || 'N/A'}</span>
                                </div>
                                <div className="flight-info">
                                  <span className="airline">{segment.marketingAirline?.name || 'N/A'}</span>
                                  {segment.marketingAirline?.flightNumber && (
                                    <span className="flight-number"> ({segment.marketingAirline.flightNumber})</span>
                                  )}
                                </div>
                              </div>
                              
                              <div className="segment-details-grid">
                                {segment.cabinType?.cabinName && (
                                  <div className="detail-item">
                                    <span className="detail-label">Class:</span>
                                    <span className="detail-value">{segment.cabinType.cabinName}</span>
                                  </div>
                                )}
                                {segment.classOfService && (
                                  <div className="detail-item">
                                    <span className="detail-label">Service Class:</span>
                                    <span className="detail-value">{segment.classOfService}</span>
                                  </div>
                                )}
                                {segment.flightDuration && (
                                  <div className="detail-item">
                                    <span className="detail-label">Duration:</span>
                                    <span className="detail-value">{segment.flightDuration}</span>
                                  </div>
                                )}
                                {segment.scheduledDepartureTime && (
                                  <div className="detail-item">
                                    <span className="detail-label">Departure:</span>
                                    <span className="detail-value">
                                      {new Date(segment.scheduledDepartureTime).toLocaleString()}
                                    </span>
                                  </div>
                                )}
                                {segment.scheduledArrivalTime && (
                                  <div className="detail-item">
                                    <span className="detail-label">Arrival:</span>
                                    <span className="detail-value">
                                      {new Date(segment.scheduledArrivalTime).toLocaleString()}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="no-data">No flight details available</div>
                      )}
                    </div>
                  </div>
                  
                  {/* Price Information Section */}
                  <div className="pnr-section">
                    <h5 className="section-title">Price Information</h5>
                    <div className="price-details">
                      {pnrDetails.totalPrice && pnrDetails.totalPrice.monetaryAmount ? (
                        <div className="price-card">
                          <div className="price-amount">
                            {pnrDetails.totalPrice.baseCurrency || ''} {pnrDetails.totalPrice.monetaryAmount}
                          </div>
                          {pnrDetails.totalPrice.baseCurrency && (
                            <div className="currency-info">
                              Base Currency: {pnrDetails.totalPrice.baseCurrency}
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="no-data">No price information available</div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              
              {viewMode === 'structured' && !pnrDetails && (
                <Message type="info" description="No detailed PNR information available" />
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowPnrDetails(false)} appearance="subtle">Close</Button>
          {pnrDetails && viewMode === 'structured' && (
            <Button 
              appearance="primary" 
              onClick={() => {
                toast.info("Printing functionality would be implemented here");
              }}
            >
              Print Itinerary
            </Button>
          )}
        </Modal.Footer>
      </Modal>

      {/* Refund Details Modal */}
      <Modal open={showRefundDetails} onClose={() => setShowRefundDetails(false)} size="md">
        <Modal.Header>
          <Modal.Title>
            Refund Eligibility - {selectedTicket?.pnr}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {refundProcessing ? (
            <div className="refund-processing">
              <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                <Loader size="lg" />
                <div style={{ marginTop: '16px', fontSize: '16px', color: '#666' }}>
                  Processing refund request...
                </div>
                <div style={{ marginTop: '8px', fontSize: '14px', color: '#999' }}>
                  Please wait while we check your refund eligibility
                </div>
              </div>
            </div>
          ) : !refundDetails ? (
            <Message type="info" description="No refund information available" />
          ) : (
            <div className="refund-details-container">
              {/* Show different content based on refund result */}
              {refundResult === 'initiated' ? (
                <div className="refund-initiated">
                  <div className="status-icon" style={{ fontSize: '48px', textAlign: 'center', marginBottom: '16px' }}>🎉</div>
                  <div style={{ textAlign: 'center' }}>
                    <h4 style={{ color: '#16a34a', marginBottom: '8px' }}>Refund Initiated Successfully!</h4>
                    <p style={{ marginBottom: '16px' }}>{refundDetails.initiationMessage}</p>
                    {refundDetails.referenceNumber && (
                      <div style={{
                        background: '#f0f9ff',
                        border: '1px solid #bae6fd',
                        borderRadius: '8px',
                        padding: '12px',
                        marginBottom: '16px'
                      }}>
                        <strong>Reference Number: </strong>{refundDetails.referenceNumber}
                      </div>
                    )}
                    <div className="refund-amount-section">
                      <div className="amount-label">Refund Amount</div>
                      <div className="amount-value">{refundDetails.currency}{refundDetails.refundAmount.toLocaleString()}</div>
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  <div className="refund-status-header">
                    {refundResult === 'error' ? (
                      <div className="refund-not-eligible">
                        <div className="status-icon">❌</div>
                        <div className="status-text">
                          <h4>Error</h4>
                          <p>{refundDetails.reason}</p>
                        </div>
                      </div>
                    ) : refundDetails.isEligible ? (
                      <div className="refund-eligible">
                        <div className="status-icon">✅</div>
                        <div className="status-text">
                          <h4>Refund Eligible</h4>
                          <p>This booking is eligible for a refund</p>
                        </div>
                      </div>
                    ) : (
                      <div className="refund-not-eligible">
                        <div className="status-icon">❌</div>
                        <div className="status-text">
                          <h4>Refund Not Eligible</h4>
                          <p>{refundDetails.reason || "This booking is not eligible for a refund"}</p>
                        </div>
                      </div>
                    )}
                  </div>

                  {refundDetails.isEligible && refundResult !== 'error' && (
                    <>
                      <div className="refund-amount-section">
                        <div className="amount-label">Refund Amount</div>
                        <div className="amount-value">{refundDetails.currency}{refundDetails.refundAmount.toLocaleString()}</div>
                      </div>

                      {refundDetails.deductions && refundDetails.deductions.length > 0 && (
                        <div className="deductions-section">
                          <h5>Deductions Applied</h5>
                          <table className="deductions-table">
                            <thead>
                              <tr>
                                <th>Description</th>
                                <th>Amount</th>
                              </tr>
                            </thead>
                            <tbody>
                              {refundDetails.deductions.map((deduction, index) => (
                                <tr key={index}>
                                  <td>{deduction.description}</td>
                                  <td>{refundDetails.currency}{deduction.amount.toLocaleString()}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      )}

                      <div className="processing-info">
                        <div className="info-icon">ℹ️</div>
                        <div className="info-text">
                          Refund processing time: {refundDetails.processingTime}
                        </div>
                      </div>
                    </>
                  )}
                </>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowRefundDetails(false)} appearance="subtle">Close</Button>
          {refundDetails && refundDetails.isEligible && (
            <Button 
              appearance="primary" 
              color="green"
              onClick={() => {
                // Implement the refund initiation logic
                initiateRefund(selectedTicket?.pnr);
                setShowRefundDetails(false);
              }}
            >
              Initiate Refund
            </Button>
          )}
        </Modal.Footer>
      </Modal>
      </div>
    </div>
  );
};

const initiateRefund = async (pnr) => {
  if (!pnr) {
    // Update modal to show error instead of toast
    setRefundDetails(prev => ({
      ...prev,
      reason: "PNR is required to initiate refund"
    }));
    setRefundResult('error');
    return;
  }

  // Show processing state in modal
  setRefundProcessing(true);
  setRefundResult(null);
  setOrchestratorLoading(true);

  try {
    // Parse the raw response into a structured JSON format
    const parsedPnrData = parseRawResponseToJson(rawResponse);

    if (!parsedPnrData) {
      throw new Error("Failed to parse PNR data");
    }

    // Add refund details to the parsed data
    parsedPnrData.refundDetails = {
      refundAmount: refundDetails?.refundAmount || 0,
      currency: refundDetails?.currency || "₹",
      reason: refundDetails?.reason || "",
      processingTime: refundDetails?.processingTime || "5-7 business days"
    };

    // Add segment details from pnrDetails if available
    if (pnrDetails && pnrDetails.segmentDetails && pnrDetails.segmentDetails.length > 0) {
      parsedPnrData.segments = pnrDetails.segmentDetails.map(segment => ({
        originAirportCode: segment.originAirportCode || segment.origin || '',
        destinationAirportCode: segment.destinationAirportCode || segment.destination || '',
        airline: segment.marketingAirline?.name || segment.airline || '',
        airlineCode: segment.marketingAirline?.code || '',
        flightNumber: segment.marketingAirline?.flightNumber || segment.flightNumber || '',
        departureDateTime: segment.scheduledDepartureTime || segment.departureDateTime || '',
        arrivalDateTime: segment.scheduledArrivalTime || segment.arrivalDateTime || '',
        status: segment.status || 'CONFIRMED',
        class: segment.cabinClass || segment.class || '',
        aircraft: segment.aircraft || ''
      }));
    }

    // Format the request data for the orchestrator
    const requestData = {
      type: "refund_request",
      data: parsedPnrData
    };

    // Log the request for debugging
    console.log("Sending refund initiation request:", requestData);

    // Call the orchestrator with the formatted request
    const result = await callOrchestrator(JSON.stringify(requestData));

    // Log the response for debugging
    console.log("Refund initiation response:", result);

    if (!result) {
      throw new Error("No response received from orchestrator");
    }

    // Handle the response
    if (result.type === "refund_request" && result.data) {
      // Update modal to show success
      setRefundDetails(prev => ({
        ...prev,
        isInitiated: true,
        initiationMessage: "Refund initiated successfully! You will receive a confirmation email shortly.",
        referenceNumber: result.data.referenceNumber || `REF${Date.now()}`
      }));
      setRefundResult('initiated');

      // Update the ticket status in the UI
      setSelectedTicket(prev => ({
        ...prev,
        status: "REFUND_INITIATED",
        refundAmount: refundDetails?.refundAmount || 0
      }));

      // Refresh the ticket list to show updated status
      fetchData();
    } else if (result.message) {
      // Handle text response in modal
      setRefundDetails(prev => ({
        ...prev,
        reason: result.message
      }));
      setRefundResult('info');
    } else {
      throw new Error("Invalid response format from orchestrator");
    }
  } catch (error) {
    console.error("Error initiating refund:", error);
    // Show error in modal instead of toast
    setRefundDetails(prev => ({
      ...prev,
      reason: `Failed to initiate refund: ${error.message}`
    }));
    setRefundResult('error');
  } finally {
    setRefundProcessing(false);
    setOrchestratorLoading(false);
  }
};

export default TicketDisplay;
